import uuid

from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase


class DataPlaneManagerTest(BraintrustAppTestBase):
    def setUp(self):
        super(DataPlaneManagerTest, self).setUp()

        sysadmin_user_id = self.ensureSysadminUserExists()

        # Create a user who is part of the org but without any
        # permissions.
        self.unpriviliged_user_id, _, self.unprivileged_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )

        # Create a user who is not part of the org.
        self.other_org_id, _ = self.createOrg()
        self.non_org_user_id, _, self.non_org_api_key = self.createUserInOrg(self.other_org_id)

        # Add the sysadmin user to the other org, so that we can generate an API
        # key that can be deleted later.
        self.addUserToOrg(sysadmin_user_id, self.other_org_id, remove_from_org_owners=True)
        self.sysadmin_api_key = self.createUserOrgApiKey(sysadmin_user_id, self.other_org_id)

    @parameterized.expand(
        [
            ("bt-hosted", "org owner", False),
            ("bt-hosted", "org unprivileged", False),
            ("bt-hosted", "non-org member", False),
            ("bt-hosted", "sysadmin", True),
            ("self-hosted", "org unprivileged", False),
            ("self-hosted", "non-org member", False),
            ("self-hosted", "sysadmin", False),
            ("self-hosted", "org owner", True),
        ]
    )
    def test_data_plane_manager(self, data_plane_mode, privilege_mode, allowed_to_provision):
        self_hosted = data_plane_mode == "self-hosted"

        # TODO: fix self-hosted provisioning in followup PR
        if self_hosted:
            self.skipTest("Self-hosted provisioning is not supported yet")

        org1_id, org1_name = self.createOrg()
        self.addUserToOrg(self.user_id, org1_id)
        org1_api_key = self.createUserOrgApiKey(self.user_id, org1_id)
        org2_id, org2_name = self.createOrg()
        self.addUserToOrg(self.user_id, org2_id)
        org2_api_key = self.createUserOrgApiKey(self.user_id, org2_id)

        org_ids = [self.org_id, org1_id, org2_id]
        org_names = [self.org_name, org1_name, org2_name]
        api_keys = [self.org_api_key, org1_api_key, org2_api_key]
        project_ids = []
        for org_name, api_key in zip(org_names, api_keys):
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/project/register",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(project_name="p-" + org_name),
            ).json()
            project_ids.append(resp["project"]["id"])

        def request_fn(force_recreate_token=False, expect_error=False):
            if privilege_mode == "org owner":
                request_api_key = self.org_api_key
            elif privilege_mode == "org unprivileged":
                request_api_key = self.unprivileged_api_key
            elif privilege_mode == "non-org member":
                request_api_key = self.non_org_api_key
            elif privilege_mode == "sysadmin":
                request_api_key = self.sysadmin_api_key
            else:
                assert False, "Invalid privilege mode"

            resp = self.run_request(
                "post",
                f"{LOCAL_APP_URL}/api/service_token/provision_data_plane_manager",
                headers=dict(Authorization=f"Bearer {request_api_key}"),
                json=dict(
                    org_id=self.org_id,
                    force_recreate_token=force_recreate_token,
                ),
                expect_error=expect_error,
            )
            return resp

        with self.subTest(
            data_plane_mode=data_plane_mode, user=privilege_mode, allowed_to_provision=allowed_to_provision
        ):
            resp = request_fn(force_recreate_token=True, expect_error=not allowed_to_provision)
            if not allowed_to_provision:
                assert resp.status_code == 401
                assert resp.text.startswith("Unauthorized")
                return

            assert resp.status_code == 200
            data = resp.json()
            assert data["data_plane_manager"]["id"] is not None
            assert (
                data["data_plane_manager"]["name"] == "bt_data_plane_manager"
                if not self_hosted
                else "data_plane_manager"
            )
            assert data["data_plane_service_token"]["name"] == "bt_data_plane_service_token"
            assert data["data_plane_service_token"]["key"] is not None
            assert data["data_plane_service_token"]["created"] is True

            manager_id = data["data_plane_manager"]["id"]
            service_token_id = data["data_plane_service_token"]["id"]
            service_token = data["data_plane_service_token"]["key"]
            if not self_hosted:
                # check that provisioned data plane manager is a sysadmin
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/api/self/me",
                    headers=dict(Authorization=f"Bearer {service_token}"),
                    json=dict(check_sysadmin=True),
                ).json()
                assert resp["id"] == manager_id
                assert resp["is_sysadmin"] is True
                # should be expected format for email
                assert resp["email"].startswith("bt::sp::")
                assert "@" not in resp["email"]
            else:
                # check that provisioned data plane manager is not a sysadmin but is in the viewers group
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/api/self/me",
                    headers=dict(Authorization=f"Bearer {service_token}"),
                    json=dict(check_sysadmin=True),
                ).json()
                assert resp["id"] == manager_id
                assert resp["is_sysadmin"] is False
                # should be expected format for email
                assert resp["email"].startswith("bt::sp::")
                assert "@" not in resp["email"]

                group = self.run_server_action(
                    self.org_api_key,
                    "getGroupMembers",
                    dict(org_name=self.org_name, group_name="Viewers"),
                ).json()
                assert any(u["id"] == manager_id for u in group["users"])

            # check that service token is unscoped
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("select id, name, user_id, org_id from api_keys where id = %s", (service_token_id,))
                    assert cursor.rowcount == 1
                    assert cursor.fetchone() == (service_token_id, "bt_data_plane_service_token", manager_id, None)

            # run get_object_info on various orgs
            if self_hosted:
                # only works on first org until the manager is invited to the other orgs
                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/api/self/get_object_info",
                    headers=dict(Authorization=f"Bearer {service_token}"),
                    json=dict(
                        object_type="project",
                        object_ids=project_ids,
                        allow_sysadmin_roles=["sysadmin"] if not self_hosted else [],
                    ),
                )
                assert resp.status_code == 200
                data = resp.json()
                assert len(data) == 1
                assert data[0]["object_id"] == project_ids[0]
                assert data[0]["parent_cols"]["organization"]["id"] == self.org_id
                assert "is_allowed_sysadmin" not in data[0]

                # invite the manager to the orgs
                self.addUserToOrg(manager_id, org1_id)
                self.addUserToOrg(manager_id, org2_id)

            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/get_object_info",
                headers=dict(Authorization=f"Bearer {service_token}"),
                json=dict(
                    object_type="project",
                    object_ids=project_ids,
                    allow_sysadmin_roles=["sysadmin"] if not self_hosted else [],
                ),
            )
            assert resp.status_code == 200
            data = resp.json()
            assert {
                (o["object_id"], o["parent_cols"]["organization"]["id"], o.get("is_allowed_sysadmin", None))
                for o in data
            } == {
                (project_id, org_id, True if not self_hosted else None)
                for project_id, org_id in zip(project_ids, org_ids)
            }

            # rerunning should return existing resources
            old_service_token_id = service_token_id
            old_service_token = service_token
            resp = request_fn(force_recreate_token=True)
            assert resp.status_code == 200
            data = resp.json()
            assert data["data_plane_manager"]["id"] == manager_id
            assert data["data_plane_service_token"]["name"] == "bt_data_plane_service_token"
            service_token_id = data["data_plane_service_token"]["id"]
            service_token = data["data_plane_service_token"]["key"]
            assert service_token_id != old_service_token_id
            assert service_token is not None
            assert service_token != old_service_token
            assert data["data_plane_service_token"]["created"] is True

            # new token provisioned
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        "select id, name, user_id, org_id from api_keys where name = %s and user_id = %s",
                        ("bt_data_plane_service_token", manager_id),
                    )
                    assert cursor.rowcount == 1
                    assert cursor.fetchone() == (service_token_id, "bt_data_plane_service_token", manager_id, None)

            # service token can be saved to data plane
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/service-token/upsert",
                headers=dict(Authorization=f"Bearer {service_token}"),
                json=dict(name="bt_data_plane_service_token", service_token=service_token),
            )
            assert resp.status_code == 200
            assert resp.json()["success"] is True

            resp = self.run_request(
                "head",
                f"{LOCAL_API_URL}/service-token/bt_data_plane_service_token",
                headers=dict(Authorization=f"Bearer {service_token}"),
            )
            assert resp.ok

    def test_data_plane_service_token_endpoints(self):
        service_token_name = "test-token"
        service_token_value = "test-token-value"

        # Org owner, unprivileged user, non-org member cannot access service token endpoints
        for api_key in [self.org_api_key, self.unprivileged_api_key, self.non_org_api_key]:
            print(f"Testing {api_key}")
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/service-token/upsert",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(name=service_token_name, service_token=service_token_value),
                expect_error=True,
            )
            assert resp.status_code == 403

            resp = self.run_request(
                "head",
                f"{LOCAL_API_URL}/service-token/{service_token_name}",
                headers=dict(Authorization=f"Bearer {api_key}"),
                expect_error=True,
            )
            assert resp.status_code == 403

            resp = self.run_request(
                "get",
                f"{LOCAL_API_URL}/service-token/{service_token_name}",
                headers=dict(Authorization=f"Bearer {api_key}"),
                expect_error=True,
            )
            assert resp.status_code == 403

        resp = self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/service_token/provision_data_plane_manager",
            headers=dict(Authorization=f"Bearer {self.sysadmin_api_key}"),
            json=dict(
                org_id=self.org_id,
                force_recreate_token=True,
            ),
        ).json()
        dp_manager_token = resp["data_plane_service_token"]["key"]

        # Data plane manager should be able to hit service token endpoints
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/service-token/upsert",
            headers=dict(Authorization=f"Bearer {dp_manager_token}"),
            json=dict(name=service_token_name, service_token=service_token_value),
        )
        assert resp.status_code == 200

        resp = self.run_request(
            "head",
            f"{LOCAL_API_URL}/service-token/{service_token_name}",
            headers=dict(Authorization=f"Bearer {dp_manager_token}"),
        )
        assert resp.status_code == 200

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/service-token/{service_token_name}",
            headers=dict(Authorization=f"Bearer {dp_manager_token}"),
        )
        assert resp.status_code == 200
        assert resp.json()["service_token"] == service_token_value

        # Non-existent service token should return 404
        resp = self.run_request(
            "head",
            f"{LOCAL_API_URL}/service-token/{service_token_name}-{uuid.uuid4()}",
            headers=dict(Authorization=f"Bearer {dp_manager_token}"),
            expect_error=True,
        )
        assert resp.status_code == 404

        # Sysadmin can also hit service token endpoints (this may change as we tighten up permissions)
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/service-token/upsert",
            headers=dict(Authorization=f"Bearer {self.sysadmin_api_key}"),
            json=dict(name=service_token_name, service_token=service_token_value),
        )
        assert resp.status_code == 200

        resp = self.run_request(
            "head",
            f"{LOCAL_API_URL}/service-token/{service_token_name}",
            headers=dict(Authorization=f"Bearer {self.sysadmin_api_key}"),
        )
        assert resp.status_code == 200

        resp = self.run_request(
            "get",
            f"{LOCAL_API_URL}/service-token/{service_token_name}",
            headers=dict(Authorization=f"Bearer {self.sysadmin_api_key}"),
        )
        assert resp.status_code == 200
        assert resp.json()["service_token"] == service_token_value

        # Non-existent service token should return 404
        resp = self.run_request(
            "head",
            f"{LOCAL_API_URL}/service-token/{service_token_name}-{uuid.uuid4()}",
            headers=dict(Authorization=f"Bearer {self.sysadmin_api_key}"),
            expect_error=True,
        )
        assert resp.status_code == 404
