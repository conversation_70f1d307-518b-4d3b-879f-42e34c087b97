import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, SYSADMIN_USER_EMAIL, BraintrustAppTestBase
from tests.helpers.resources import clear_resource_limits, get_resource_limits, set_resource_limit

ALL_PRIVILEGE_MODES = ["org owner", "sysadmin", "org unprivileged", "non-org member"]
OBJECT_OWNER_PRIVILEGE_MODES = ["org owner", "sysadmin"]
SYSADMIN_ONLY_PRIVILEGE_MODES = ["sysadmin"]


class SysadminTest(BraintrustAppTestBase):
    def setUp(self):
        super(SysadminTest, self).setUp()

        sysadmin_user_id = self.ensureSysadminUserExists()
        # Create a user who is part of the org but without any
        # permissions.
        self.unpriviliged_user_id, _, self.unprivileged_api_key = self.createUserInOrg(
            self.org_id, remove_from_org_owners=True
        )

        # Create a user who is not part of the org.
        self.other_org_id, _ = self.createOrg()
        self.non_org_user_id, _, self.non_org_api_key = self.createUserInOrg(self.other_org_id)

        # Add the sysadmin user to the other org, so that we can generate an API
        # key that can be deleted later.
        self.addUserToOrg(sysadmin_user_id, self.other_org_id, remove_from_org_owners=True)
        self.sysadmin_api_key = self.createUserOrgApiKey(sysadmin_user_id, self.other_org_id)

    # Runs the given request_fn against all users in the org. The request_fn
    # expects an api_key and an expect_error argument, and makes sure running
    # the request results in the expected error/lack of error.
    def execute_against_all_users(self, request_fn, privilege_modes):
        with self.subTest(user="org owner"):
            request_fn(self.org_api_key, expect_error=(not ("org owner" in privilege_modes)))
        with self.subTest(user="sysadmin"):
            request_fn(self.sysadmin_api_key, expect_error=(not ("sysadmin" in privilege_modes)))
        with self.subTest(user="org unprivileged"):
            request_fn(self.unprivileged_api_key, expect_error=(not ("org unprivileged" in privilege_modes)))
        with self.subTest(user="non-org member"):
            request_fn(self.non_org_api_key, expect_error=(not ("non-org member" in privilege_modes)))

    def test_brainstore_global_backfill_status(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        def request_fn(api_key, expect_error):
            self.run_request(
                "get",
                f"{LOCAL_API_URL}/brainstore/backfill/status",
                headers=dict(Authorization=f"Bearer {api_key}"),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, ALL_PRIVILEGE_MODES)

    def test_brainstore_backfill_run(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/backfill/run",
                headers=dict(Authorization=f"Bearer {api_key}"),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, ALL_PRIVILEGE_MODES)

    def test_brainstore_project_backfill_status(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        project_id = logger.project.id

        def request_fn(api_key, expect_error):
            self.run_request(
                "get",
                f"{LOCAL_API_URL}/brainstore/backfill/status/project/{project_id}",
                headers=dict(Authorization=f"Bearer {api_key}"),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_object_backfill_status(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        experiment = braintrust.init(project="p")
        experiment_id = experiment.id
        object_id = f"experiment:{experiment_id}"

        def request_fn(api_key, expect_error):
            self.run_request(
                "get",
                f"{LOCAL_API_URL}/brainstore/backfill/status/object/{object_id}",
                headers=dict(Authorization=f"Bearer {api_key}"),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_segment_object_id(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        experiment = braintrust.init(project="p")
        experiment.log(input="foo", output="bar", expected="baz", scores=dict(accuracy=0.9))
        braintrust.flush()
        self.catchupBrainstore()

        experiment_id = experiment.id
        object_id = f"experiment:{experiment_id}"
        with self.connect_api_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "select segment_id from brainstore_global_store_segment_id_to_liveness where object_id = %s",
                    (object_id,),
                )
                result = cursor.fetchone()
                if result is None:
                    raise Exception(f"Segment ID not found for object {object_id}")
                segment_id = result[0]

        def request_fn(api_key, expect_error):
            resp = self.run_request(
                "get",
                f"{LOCAL_API_URL}/brainstore/segment/{segment_id}",
                headers=dict(Authorization=f"Bearer {api_key}"),
                expect_error=expect_error,
            )
            if not expect_error:
                self.assertEqual(resp.json()["object_id"], object_id)

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_track_objects(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        project_id = logger.project.id

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/backfill/track",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(tracked_objects=[dict(project_id=project_id, object_type="project_logs")]),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_enable_tracking(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        project_id = logger.project.id

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/backfill/enable",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(project_id=project_id),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_delete_tracking(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        project_id = logger.project.id

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/backfill/delete",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(project_id=project_id, should_reset_historical_backfill=False),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_optimize_object(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        logger.log(input="foo")
        braintrust.flush()
        project_id = logger.project.id

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/backfill/optimize",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(object_id=f"project_logs:{project_id}"),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_get_vacuum_status(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        logger.log(input="foo")
        braintrust.flush()
        project_id = logger.project.id

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/vacuum/status",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(object_ids=[f"project_logs:{project_id}"]),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_reset_vacuum_state(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        logger.log(input="foo")
        braintrust.flush()
        project_id = logger.project.id

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/vacuum/reset_state",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(object_ids=[f"project_logs:{project_id}"]),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_brainstore_vacuum_object(self):
        if BraintrustAppTestBase.skip_brainstore():
            self.skipTest("Brainstore is not enabled")

        logger = braintrust.init_logger(project="p")
        logger.log(input="foo")
        braintrust.flush()
        object_id = f"project_logs:{logger.project.id}"

        def request_fn(api_key, expect_error):
            self.run_request(
                "post",
                f"{LOCAL_API_URL}/brainstore/vacuum/object/{object_id}",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(segment_id_cursor=None),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, OBJECT_OWNER_PRIVILEGE_MODES)

    def test_admin_fetch_all_orgs(self):
        def request_fn(api_key, expect_error):
            self.run_server_action(api_key, "adminFetchAllOrgs", dict(), expect_error=expect_error)

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

    def test_admin_fetch_all_projects(self):
        def request_fn(api_key, expect_error):
            self.run_server_action(
                api_key, "adminFetchAllProjects", dict(orgName=self.org_name), expect_error=expect_error
            )

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

    def test_admin_fetch_objects(self):
        logger = braintrust.init_logger(project="p")
        project_name = logger.project.name

        def request_fn(api_key, expect_error):
            self.run_server_action(
                api_key,
                "adminFetchObjects",
                dict(orgName=self.org_name, projectName=project_name),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

    def test_admin_find_object(self):
        experiment = braintrust.init_experiment(project="p")
        experiment_id = experiment.id
        object_id = f"experiment:{experiment_id}"

        def request_fn(api_key, expect_error):
            self.run_server_action(api_key, "adminFindObject", dict(objectId=object_id), expect_error=expect_error)

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

    def test_admin_restore_object(self):
        experiment = braintrust.init_experiment(project="p")
        experiment_id = experiment.id

        def request_fn(api_key, expect_error):
            self.run_server_action(
                api_key,
                "adminRestoreObject",
                dict(objectType="experiment", objectId=experiment_id),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

    def test_admin_update_org_tier(self):
        clear_resource_limits(self.org_id)
        self.assertEqual(get_resource_limits(self.org_id), {})

        def request_fn(api_key, expect_error):
            self.run_server_action(
                api_key,
                "adminUpdateResourceLimit",
                dict(orgId=self.org_id, spanLimit=100000, logBytesLimit=100000),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_dataset_row_actions": (7, 25000),
                "num_dataset_row_actions_calendar_months": (1, 100000),
                "num_log_bytes": (7, 25000),
                "num_log_bytes_calendar_months": (1, 100000),
                "num_private_experiment_row_actions": (7, 25000),
                "num_private_experiment_row_actions_calendar_months": (1, 100000),
                "num_production_log_row_actions": (7, 25000),
                "num_production_log_row_actions_calendar_months": (1, 100000),
            },
        )

    def test_admin_update_existing(self):
        set_resource_limit(self.org_id, "num_private_experiment_row_actions", (7, 100_000))
        set_resource_limit(self.org_id, "num_private_experiment_row_actions_calendar_months", (1, 400_000))
        set_resource_limit(self.org_id, "num_log_bytes", (7, 100_000))
        set_resource_limit(self.org_id, "num_log_bytes_calendar_months", (1, 400_000))

        UNLIMITED = -1

        def request_fn(api_key, expect_error):
            self.run_server_action(
                api_key,
                "adminUpdateResourceLimit",
                dict(orgId=self.org_id, spanLimit=100000, logBytesLimit=UNLIMITED),
                expect_error=expect_error,
            )

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

        self.assertEqual(
            get_resource_limits(self.org_id),
            {
                "num_dataset_row_actions": (7, 25000),
                "num_dataset_row_actions_calendar_months": (1, 100000),
                "num_log_bytes": None,
                "num_log_bytes_calendar_months": None,
                "num_private_experiment_row_actions": (7, 25000),
                "num_private_experiment_row_actions_calendar_months": (1, 100000),
                "num_production_log_row_actions": (7, 25000),
                "num_production_log_row_actions_calendar_months": (1, 100000),
            },
        )

    def test_admin_create_brainstore_license(self):
        def request_fn(api_key, expect_error):
            self.run_server_action(
                api_key, "adminCreateBrainstoreLicense", dict(orgId=self.org_id), expect_error=expect_error
            )

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

    def test_admin_archive_org(self):
        (org_id, _) = self.createOrg()

        def request_fn(api_key, expect_error):
            self.run_server_action(api_key, "adminArchiveOrg", dict(orgId=org_id), expect_error=expect_error)

        self.execute_against_all_users(request_fn, SYSADMIN_ONLY_PRIVILEGE_MODES)

    def test_sysadmin_me(self):
        def request_fn(api_key, expect_sysadmin):
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/me",
                headers=dict(Authorization=f"Bearer {api_key}"),
                json=dict(check_sysadmin=True),
            )
            resp_json = resp.json()
            self.assertEqual(resp_json["is_sysadmin"], expect_sysadmin)

        with self.subTest(user="org owner"):
            request_fn(self.org_api_key, expect_sysadmin=False)
        with self.subTest(user="sysadmin"):
            request_fn(self.sysadmin_api_key, expect_sysadmin=True)
        with self.subTest(user="org unprivileged"):
            request_fn(self.unprivileged_api_key, expect_sysadmin=False)
        with self.subTest(user="non-org member"):
            request_fn(self.non_org_api_key, expect_sysadmin=False)
