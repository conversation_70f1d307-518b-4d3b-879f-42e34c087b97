import atexit
import json
import os
import random
import sys
import time
import unittest
from typing import Optional
from uuid import uuid4

import braintrust
import psycopg2
import requests
from braintrust_local import api_db_util, app_db_util
from braintrust_local.backfill_util import catchup_brainstore_backfill
from braintrust_local.bt_unittest_util import get_args_for_pid
from braintrust_local.test_proxy_util import get_test_proxy_server_config
from braintrust_local.util import ensure_not_none
from braintrust_local.vacuum_util import catchup_brainstore_vacuum, force_brainstore_revacuum

LOCAL_USER_ID = "fac36c53-c882-458b-bf80-60d06c3e8a0d"
LOCAL_API_URL = "http://localhost:8000"
LOCAL_APP_URL = "http://localhost:3000"
SYSADMIN_USER_EMAIL = "<EMAIL>"
TEST_ARGS = get_args_for_pid(os.getpid())


class BraintrustAppTestBase(unittest.TestCase):
    """Base test fixture for all braintrust tests connecting to the main app.
    The user is expected to have nodeJS and AWS lambda running locally."""

    @staticmethod
    def connect_app_db():
        return psycopg2.connect(app_db_util.get_app_db_url())

    @staticmethod
    def connect_api_db():
        return psycopg2.connect(api_db_util.get_api_db_url())

    @staticmethod
    def _create_test_org(prefix="", org_id=None):
        """Connects to the local supabase instance and creates a uniquely-named
        org for this unit test. Returns the org id and org name."""

        org_id = org_id or str(uuid4())

        # create a unique but time sortable org name in case you need to poke
        # around in the UI or database.
        entropy = format(random.getrandbits(24), "x")  # 6 hex chars
        timestamp = round(time.time() * 1000)
        org_name_in = f"_unit_test_org_{prefix}_{timestamp}_{entropy}"

        # XXX: make sure to update the app/.env.development CSP_ENABLED_ORG_NAMES to match org name prefix
        proxy_config = get_test_proxy_server_config()
        proxy_url = f"http://{proxy_config.host}:{proxy_config.port}"
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    insert into organizations (id, name, proxy_url)
                    values (%s, %s, %s)
                    returning id, name
                    """,
                    [org_id, org_name_in, proxy_url],
                )
                result = cursor.fetchone()
                assert result is not None
                org_id_out = result[0]

                org_name = result[1]
                assert org_name == org_name_in
                assert org_id_out == org_id

                # Add model provider as org secrets if we are in "update" mode.
                if TEST_ARGS.get("update"):
                    for secret_type, secret_name in [
                        ("openai", "OPENAI_API_KEY"),
                        ("anthropic", "ANTHROPIC_API_KEY"),
                        ("vertex", "VERTEX_AI_API_KEY"),
                    ]:
                        api_key = os.environ.get(secret_name)
                        metadata = None
                        if secret_type == "vertex":
                            project_id = os.environ.get("GCP_PROJECT_ID")
                            if project_id:
                                metadata = {
                                    "project": project_id,
                                    "authType": "access_token",
                                    "api_base": "",
                                    "supportsStreaming": True,
                                    "excludeDefaultModels": False,
                                }

                        if api_key:
                            cursor.execute(
                                f"""
                                INSERT INTO secrets.org_secrets(org_id, type, name, secret, metadata) VALUES (%s, %s, %s, %s, %s)
                                """,
                                (org_id, secret_type, secret_name, api_key, metadata and json.dumps(metadata)),
                            )

        return org_id, org_name

    @staticmethod
    def tearDownDb(org_id):
        c = Capture()
        atexit.unregister(c)
        for f in c.captured:
            if "finalize" in f.__name__:
                f()

        BraintrustAppTestBase.delete_org(org_id)

    @staticmethod
    def delete_org(org_id):
        # Delete the org from the db. Since we modified all foreign key
        # constraints to cascade (except for the `org_secrets` table), we should
        # delete any linked entries along the way.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    f"""
                    delete from secrets.org_secrets
                    where org_id = %s
                    """,
                    [org_id],
                )
                cursor.execute(
                    """
                    delete from organizations
                    where id = %s
                    """,
                    [org_id],
                )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.additional_orgs = set()
        self.additional_users = set()
        self.additional_service_accounts = set()

    def createOrg(self, org_id=None):
        test_name = self._testMethodName
        org_id, org_name = BraintrustAppTestBase._create_test_org(prefix=test_name, org_id=org_id)
        self.additional_orgs.add(org_id)
        return org_id, org_name

    def createUserEmail(self):
        return "_unit_test_user_" + str(uuid4())

    def createUser(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            email = self.createUserEmail()
            with conn.cursor() as cursor:
                cursor.execute("insert into users(email) values (%s) returning id, auth_id", (email,))
                user_id, auth_id = ensure_not_none(cursor.fetchone())
                self.additional_users.add(user_id)
                return user_id, auth_id

    def createServiceAccount(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            name = str(uuid4())
            result = self.run_server_action(
                self.org_api_key,
                "addMembers",
                dict(orgId=self.org_id, users=dict(service_accounts=[dict(name=name, token_name=None)])),
            ).json()
            user_id = result["added_users"][0]["id"]
            with conn.cursor() as cursor:
                cursor.execute(
                    "select auth_id from users where id = %s",
                    (user_id,),
                )
                (auth_id,) = ensure_not_none(cursor.fetchone())
                self.additional_service_accounts.add(user_id)
                return user_id, auth_id

    def getUserEmail(self, user_id):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select email from users where id = %s", (user_id,))
                return ensure_not_none(cursor.fetchone())[0]

    def createUserOrgApiKey(self, user_id: str, org_id: Optional[str]) -> str:
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "select create_api_key(users.auth_id, %s, 'test') from users where id=%s",
                    (org_id, user_id),
                )
                return ensure_not_none(cursor.fetchone())[0]

    def addUserToOrg(self, user_id, org_id, remove_from_org_owners=False, cursor=None):
        if cursor:
            if remove_from_org_owners:
                cursor.execute(
                    "select add_member_to_org_unchecked(%s, %s, null)",
                    (
                        user_id,
                        org_id,
                    ),
                )
            else:
                cursor.execute(
                    "select add_member_to_org_unchecked(%s, %s, array [get_group_id(%s, 'Owners')])",
                    (
                        user_id,
                        org_id,
                        org_id,
                    ),
                )
        else:
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    self.addUserToOrg(user_id, org_id, remove_from_org_owners, cursor)

    def createUserInOrg(self, org_id: str, remove_from_org_owners=False):
        user_id, auth_id = self.createUser()
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                self.addUserToOrg(user_id, org_id, remove_from_org_owners, cursor)
                cursor.execute(
                    "select create_api_key(users.auth_id, %s, 'test') from users where id=%s",
                    (org_id, user_id),
                )
                return user_id, auth_id, ensure_not_none(cursor.fetchone())[0]

    def ensureSysadminUserExists(self):
        # Ensure the SYSADMIN_USER_EMAIL exists and is NOT part of the test org.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("insert into users(email) values (%s) on conflict do nothing", (SYSADMIN_USER_EMAIL,))
                cursor.execute("select id from users where email = %s", (SYSADMIN_USER_EMAIL,))
                sysadmin_user_id = cursor.fetchone()[0]
                cursor.execute(
                    "delete from members where user_id = %s and org_id = %s", (sysadmin_user_id, self.org_id)
                )
                return sysadmin_user_id

    def createServiceAccountInOrg(self, org_id: str, remove_from_org_owners=False, token_name="service token"):
        user_id, auth_id = self.createServiceAccount()
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                self.addUserToOrg(user_id, org_id, remove_from_org_owners, cursor)
                cursor.execute(
                    "select create_api_key_full(users.auth_id, %s, %s, true)->'api_key'->>'key' from users where id=%s",
                    (org_id, token_name, user_id),
                )
                return user_id, auth_id, ensure_not_none(cursor.fetchone())[0]

    def setUp(self):
        self.setUpFull()

    def setUpFull(self, skip_login=False, org_id=None):
        super().setUp()

        if TEST_ARGS.get("verbose"):
            self.maxDiff = None

        self.org_id, self.org_name = self.createOrg(org_id=org_id)
        if org_id:
            assert self.org_id == org_id, f"org_id mismatch: {self.org_id} != {org_id}"
        self.user_id, self.user_auth_id, self.org_api_key = self.createUserInOrg(self.org_id)
        self.user_email = self.getUserEmail(self.user_id)

        (
            self.service_account_id,
            self.service_account_auth_id,
            self.service_token,
        ) = self.createServiceAccountInOrg(self.org_id)

        # If they exist, add the default local user to the org, so that they can
        # inspect unit test objects within the webapp.
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select 1 from users where id = %s", (LOCAL_USER_ID,))
                has_local_user = bool(cursor.fetchone())
        if has_local_user:
            self.addUserToOrg(LOCAL_USER_ID, self.org_id)

        braintrust._internal_reset_global_state()
        if skip_login:
            return
        try:
            braintrust.login(
                org_name=self.org_name,
                app_url=LOCAL_APP_URL,
                api_key=self.org_api_key,
                force_login=True,
            )
        except Exception as e:
            self.tearDown()
            raise e

    def tearDown(self):
        for org_id in self.additional_orgs:
            BraintrustAppTestBase.tearDownDb(org_id)
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                for user_id in self.additional_users:
                    cursor.execute("delete from users where id = %s", (user_id,))
                for user_id in self.additional_service_accounts:
                    cursor.execute("delete from users where id = %s", (user_id,))

    def registerProject(self, args):
        args["org_id"] = self.org_id
        resp = braintrust.app_conn().post_json("api/project/register", args)
        return resp

    def registerPromptSession(self, args):
        args["org_name"] = self.org_name
        resp = braintrust.app_conn().post_json("api/prompt_session/register", args)
        return resp

    def registerDataset(self, args):
        args["org_id"] = self.org_id
        resp = braintrust.app_conn().post_json("api/dataset/register", args)
        return resp

    def registerExperiment(self, args):
        args["org_id"] = self.org_id
        resp = braintrust.app_conn().post_json("api/experiment/register", args)
        return resp

    # Note that we don't actually need to run the catch up in order to query the
    # latest data for an object in brainstore, because we will immediately start
    # inserting records for new objects into the object's realtime WAL.
    #
    # But the catchup can still be useful for testing the backfill logic
    # specifically. This function ensures that all objects have been backfilled
    # up to the point of the last-snapshotted max sequence id in the logs table.
    def catchupBrainstore(self):
        try:
            catchup_brainstore_backfill(
                {
                    "timeout": 60,
                }
            )
        except Exception as e:
            self.fail(str(e))

    def catchupBrainstoreWithCompaction(self, object_id):
        self.catchupBrainstore()
        start = time.time()
        while True:
            if time.time() - start > 60:
                self.fail("Timed out waiting for compaction")

            resp = self.run_request(
                "get",
                f"{LOCAL_API_URL}/brainstore/backfill/status/object/{object_id}",
            )

            data = resp.json()

            last_processed_xact_id = data["last_processed_xact_id"]
            all_compacted = True
            for s in data["segments"].values():
                if (
                    s["last_compacted_index_meta"] is None
                    or s["last_compacted_index_meta"]["xact_id"] != last_processed_xact_id
                ):
                    all_compacted = False
                    break
            if all_compacted:
                return
            else:
                time.sleep(0.1)

    def catchupBrainstoreVacuum(self, object_ids):
        try:
            catchup_brainstore_vacuum(object_ids)
        except Exception as e:
            self.fail(str(e))

    def forceBrainstoreRevacuum(self, object_ids):
        try:
            catchup_brainstore_vacuum(object_ids)
            force_brainstore_revacuum(object_ids)
        except Exception as e:
            self.fail(str(e))

    def brainstore_query_args(self, default_traces=True, realtime=True):
        return {
            "use_brainstore": True,
            "brainstore_default_traces": default_traces,
            "brainstore_realtime": realtime,
            "brainstore_skip_backfill_check": True,
        }

    def make_mode_args(self, mode):
        mode_args = {"use_brainstore": False}  # TODO(BRAINSTORE): Fix this to use brainstore
        if mode == "postgres":
            mode_args["use_columnstore"] = False
        elif mode == "duckdb":
            mode_args["_debug_use_duckdb"] = True
        elif mode == "brainstore":
            mode_args.update(self.brainstore_query_args())
        return mode_args

    def run_request(
        self, verb, *args, expect_error=False, allow_500_errors=False, skip_enable_audit=False, **request_kwargs
    ):
        request_kwargs = {**request_kwargs}
        if "headers" not in request_kwargs:
            request_kwargs["headers"] = {"Authorization": f"Bearer {self.org_api_key}"}
        if not skip_enable_audit:
            request_kwargs["headers"]["x-bt-enable-audit"] = "true"
        resp = getattr(requests, verb)(*args, **request_kwargs)
        if expect_error:
            self.assertFalse(resp.ok)
            if not allow_500_errors:
                self.assertNotEqual(resp.status_code, 500)
        else:
            self.assertTrue(resp.ok, resp.text)
        return resp

    def run_server_action(self, api_key, function_name, function_args, org_name=None, expect_error=False):
        return self.run_request(
            "post",
            f"{LOCAL_APP_URL}/api/actions/{function_name}",
            headers=dict(Authorization=f"Bearer {api_key}"),
            json=dict(
                auth_info=dict(
                    org_name=org_name,
                ),
                function_args=function_args,
            ),
            expect_error=expect_error,
        )

    def grant_acl(self, body, expect_error=False):
        return self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/acl",
            json=body,
            expect_error=expect_error,
        )

    @staticmethod
    def get_owner_role_id():
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select get_owner_role_id()")
                return ensure_not_none(cursor.fetchone())[0]

    @staticmethod
    def flush_proxy_promises():
        TEST_PROXY_SERVER_CONFIG = get_test_proxy_server_config()
        test_proxy_base_url = f"http://{TEST_PROXY_SERVER_CONFIG.host}:{TEST_PROXY_SERVER_CONFIG.port}"
        # We flush the api-ts first, because the API container may send requests
        # to the test proxy.
        requests.post(f"{LOCAL_API_URL}/debug/flush").raise_for_status()
        requests.post(f"{test_proxy_base_url}/debug/flush").raise_for_status()

    def _get_testenv(self):
        env = os.environ.copy()
        env["BRAINTRUST_ORG_NAME"] = self.org_name
        env["BRAINTRUST_APP_URL"] = LOCAL_APP_URL
        env["BRAINTRUST_API_KEY"] = self.org_api_key
        env["OPENAI_BASE_URL"] = f"{get_test_proxy_base_url()}/v1"
        env["OPENAI_API_KEY"] = self.org_api_key
        env["ANTHROPIC_BASE_URL"] = f"{get_test_proxy_base_url()}/v1/anthropic"
        env["ANTHROPIC_API_KEY"] = self.org_api_key
        if sys.platform == "darwin":
            env["BRAINTRUST_INTERNAL_PY_BUNDLE_PLATFORM_OVERRIDE"] = "aarch64-apple-darwin"
        return env

    @staticmethod
    def skip_s3():
        return bool(os.environ.get("BT_UNITTEST_SKIP_S3"))

    @staticmethod
    def skip_brainstore():
        return bool(os.environ.get("BT_UNITTEST_SKIP_BRAINSTORE"))

    @staticmethod
    def skip_webhook():
        return bool(os.environ.get("BT_UNITTEST_SKIP_WEBHOOK"))


# https://stackoverflow.com/questions/16042463/how-can-i-get-the-list-of-registered-atexit-functions-in-python3
class Capture:
    def __init__(self):
        self.captured = []

    def __eq__(self, other):
        self.captured.append(other)
        return False

    def __call__(self, *args, **kwargs):
        del args, kwargs
        pass


def get_test_proxy_base_url():
    TEST_PROXY_SERVER_CONFIG = get_test_proxy_server_config()
    return f"http://{TEST_PROXY_SERVER_CONFIG.host}:{TEST_PROXY_SERVER_CONFIG.port}"


def make_v1_url(object_type, *args):
    ret = f"{LOCAL_API_URL}/v1/{object_type}"
    for arg in args:
        ret += f"/{arg}"
    return ret
