import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "#/ui/dialog";
import { type DialogProps } from "@radix-ui/react-dialog";
import {
  type GetUserGroupWithObjectPermissionsInput,
  type getUserGroupWithObjectPermissions,
} from "../../object-permissions-actions";
import { useContext } from "react";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "#/ui/tabs";
import { AccessTables } from "./access-tables";
import { useQueryFunc } from "#/utils/react-query";
import { OrgUsersContext } from "#/utils/org-users-context";
import { bucketUsersAndGroupsByAccess } from "../configuration/permissions/utils";
import { type fetchOrgGroups } from "#/utils/org-groups";
import { ShieldCheck, User2, Server } from "lucide-react";

export const ObjectPermissionsDialog = ({
  orgName,
  projectName,
  objectName,
  objectType,
  objectId,
  ...rest
}: {
  orgName: string;
  projectName?: string;
  objectName: string;
  objectType: GetUserGroupWithObjectPermissionsInput["objectType"];
  objectId: string;
} & DialogProps) => {
  const orgUsers = useContext(OrgUsersContext);

  const {
    data: usersAndGroupsWithAccess,
    isLoading: isLoadingUsersAndGroupsWithAccess,
    invalidate: invalidateUsersAndGroupsWithAccess,
  } = useQueryFunc<typeof getUserGroupWithObjectPermissions>({
    fName: "getUserGroupWithObjectPermissions",
    args: {
      org_name: orgName,
      project_name: projectName,
      objectType,
      name: objectName,
      skipRestrictObjectTypeCheck: true,
    },
  });

  const {
    data: orgGroups,
    isLoading: isLoadingOrgGroups,
    invalidate: invalidateOrgGroups,
  } = useQueryFunc<typeof fetchOrgGroups>({
    fName: "fetchOrgGroups",
    args: {
      orgName,
    },
  });

  if (
    isLoadingOrgGroups ||
    isLoadingUsersAndGroupsWithAccess ||
    !usersAndGroupsWithAccess ||
    !orgGroups
  ) {
    return null;
  }

  const {
    groupsWithAccess,
    groupsWithInheritedAccess,
    usersWithAccess,
    usersWithInheritedAccess,
    serviceAccountsWithAccess,
    serviceAccountsWithInheritedAccess,
    groupsWithoutAccess,
    usersWithoutAccess,
    serviceAccountsWithoutAccess,
  } = bucketUsersAndGroupsByAccess(
    usersAndGroupsWithAccess,
    Object.values(orgGroups),
    Object.values(orgUsers.orgUsers),
  );

  return (
    <Dialog {...rest}>
      <DialogContent className="block h-[80vh] w-full sm:max-w-screen-sm">
        <DialogHeader className="mb-4">
          <DialogTitle className="pr-3 leading-snug">
            Object permissions for {objectType} {objectName}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="groups" className="grow">
          <TabsList className="flex w-full">
            <TabsTrigger value="groups" className="flex flex-1 gap-2">
              <ShieldCheck className="size-3" />
              Permission groups
            </TabsTrigger>
            <TabsTrigger value="users" className="flex flex-1 gap-2">
              <User2 className="size-3" />
              Users
            </TabsTrigger>
            <TabsTrigger value="service_accounts" className="flex flex-1 gap-2">
              <Server className="size-3" />
              Service accounts
            </TabsTrigger>
          </TabsList>
          <TabsContent value="groups">
            <AccessTables
              onSetPermissions={() => {
                invalidateUsersAndGroupsWithAccess();
                invalidateOrgGroups();
              }}
              itemsWithAccess={groupsWithAccess}
              itemsWithInheritedAccess={groupsWithInheritedAccess}
              itemsWithoutAccess={groupsWithoutAccess}
              userObjectType="group"
              objectId={objectId}
              objectType={objectType}
              objectName={objectName}
            />
          </TabsContent>
          <TabsContent value="users">
            <AccessTables
              onSetPermissions={() => {
                invalidateUsersAndGroupsWithAccess();
                invalidateOrgGroups();
              }}
              itemsWithAccess={usersWithAccess}
              itemsWithInheritedAccess={usersWithInheritedAccess}
              itemsWithoutAccess={usersWithoutAccess}
              userObjectType="user"
              objectId={objectId}
              objectType={objectType}
              objectName={objectName}
            />
          </TabsContent>
          <TabsContent value="service_accounts">
            <AccessTables
              onSetPermissions={() => {
                invalidateUsersAndGroupsWithAccess();
                invalidateOrgGroups();
              }}
              itemsWithAccess={serviceAccountsWithAccess}
              itemsWithInheritedAccess={serviceAccountsWithInheritedAccess}
              itemsWithoutAccess={serviceAccountsWithoutAccess}
              userObjectType="service_account"
              objectId={objectId}
              objectType={objectType}
              objectName={objectName}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
