import { toast } from "sonner";
import { Input } from "#/ui/input";
import { Edit3Icon, Search, Trash2Icon } from "lucide-react";
import { useState } from "react";
import { type UserObjectType } from "#/ui/permissions/permissions-types";
import { type AccessSummary } from "../../object-permissions-types";
import { Button } from "#/ui/button";
import { Dialog, DialogTrigger } from "#/ui/dialog";
import ObjectLevelPermissionsDialog from "#/ui/permissions/object-level-permissions-dialog";
import { type AclObjectType } from "@braintrust/core/typespecs";
import ProjectLevelPermissionsDialog from "#/ui/permissions/project-level-permissions-dialog";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import {
  type LoadedBtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { removeDirectAccess } from "#/ui/permissions/form-helpers";

export function AccessTables({
  userObjectType,
  itemsWithAccess,
  itemsWithInheritedAccess,
  itemsWithoutAccess,
  onSetPermissions,
  objectId,
  objectType,
  objectName,
}: {
  objectId: string;
  objectType: AclObjectType;
  objectName: string;
  userObjectType: UserObjectType | "service_account";
  itemsWithAccess: AccessSummary[];
  itemsWithInheritedAccess: AccessSummary[];
  itemsWithoutAccess: AccessSummary[];
  onSetPermissions: VoidFunction;
}) {
  const [searchQuery, setSearchQuery] = useState("");
  const userObjectTypeLabel =
    userObjectType === "user"
      ? "Users"
      : userObjectType === "service_account"
        ? "Service accounts"
        : "Groups";

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };

  const filteredItemsWithAccess = itemsWithAccess.filter((item) =>
    item.label.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const filteredItemsWithInheritedAccess = itemsWithInheritedAccess.filter(
    (item) => item.label.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const filteredItemsWithoutAccess = itemsWithoutAccess.filter((item) =>
    item.label.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const showItemsWithAccess = filteredItemsWithAccess.length > 0;
  const showItemsWithInheritedAccess =
    filteredItemsWithInheritedAccess.length > 0;

  const showItemsWithoutAccess = filteredItemsWithoutAccess.length > 0;

  let showGlobalEmptyMessage = false;
  let globalEmptyMessage = "";
  if (!searchQuery && !showItemsWithAccess && !showItemsWithInheritedAccess) {
    showGlobalEmptyMessage = true;
    globalEmptyMessage = `There are no ${userObjectTypeLabel.toLowerCase()} with access to this object`;
  } else if (
    searchQuery &&
    !showItemsWithAccess &&
    !showItemsWithInheritedAccess &&
    !showItemsWithoutAccess
  ) {
    showGlobalEmptyMessage = true;
    globalEmptyMessage = `No ${userObjectTypeLabel.toLowerCase()} found`;
  }

  return (
    <div>
      <div className="relative mt-4 flex">
        <Search className="pointer-events-none absolute left-2 top-3 size-3 text-primary-500" />
        <Input
          placeholder="Search"
          className="h-9 flex-1 pl-6"
          onChange={handleSearchChange}
          value={searchQuery}
        />
      </div>
      {showGlobalEmptyMessage && (
        <div className="py-4 text-sm text-primary-500">
          {globalEmptyMessage}
        </div>
      )}

      {showItemsWithAccess && (
        <>
          <SectionHeading>{userObjectTypeLabel} with access</SectionHeading>
          {filteredItemsWithAccess.map((item) => (
            <AccessTableRow
              userObjectType={
                userObjectType === "service_account" ? "user" : userObjectType
              }
              userOrGroupId={item.userOrGroupId}
              objectId={objectId}
              objectType={objectType}
              objectName={objectName}
              userOrGroupLabel={item.label}
              key={item.userOrGroupId}
              title={item.label}
              description={item.accessLabel}
              onSetPermissions={onSetPermissions}
              showRemoveButton
            />
          ))}
        </>
      )}

      {showItemsWithInheritedAccess && (
        <>
          <SectionHeading>
            {userObjectTypeLabel} with inherited access
          </SectionHeading>
          {filteredItemsWithInheritedAccess.map((item) => (
            <AccessTableRow
              userObjectType={
                userObjectType === "service_account" ? "user" : userObjectType
              }
              userOrGroupId={item.userOrGroupId}
              objectId={objectId}
              objectType={objectType}
              objectName={objectName}
              userOrGroupLabel={item.label}
              key={item.userOrGroupId}
              title={item.label}
              description={
                item.accessLabel +
                ` (${item.inheritanceLabels?.join(", ") ?? ""})`
              }
              onSetPermissions={onSetPermissions}
            />
          ))}
        </>
      )}
      {showItemsWithoutAccess && (
        <>
          <SectionHeading>{userObjectTypeLabel} with no access</SectionHeading>
          {filteredItemsWithoutAccess.map((item) => (
            <AccessTableRow
              userObjectType={
                userObjectType === "service_account" ? "user" : userObjectType
              }
              userOrGroupId={item.userOrGroupId}
              objectId={objectId}
              objectType={objectType}
              objectName={objectName}
              userOrGroupLabel={item.label}
              key={item.userOrGroupId}
              title={item.label}
              onSetPermissions={onSetPermissions}
            />
          ))}
        </>
      )}
    </div>
  );
}

const SectionHeading = ({ children }: React.PropsWithChildren<{}>) => (
  <h3 className="mb-2 pt-6 text-sm font-medium">{children}</h3>
);

async function onRemoveAccess({
  objectId,
  objectType,
  userObjectType,
  userOrGroupId,
  getOrRefreshToken,
  apiUrl,
}: {
  objectId: string;
  objectType: AclObjectType;
  userObjectType: UserObjectType;
  userOrGroupId: string;
  getOrRefreshToken: () => Promise<LoadedBtSessionToken>;
  apiUrl: string;
}) {
  const sessionToken = await getOrRefreshToken();
  await removeDirectAccess({
    objectId,
    objectType,
    userOrGroupId,
    userObjectType,
    apiUrl,
    sessionToken,
  });
}

const AccessTableRow = ({
  title,
  description,
  objectId,
  objectType,
  objectName,
  userOrGroupLabel,
  userOrGroupId,
  userObjectType,
  onSetPermissions,
  showRemoveButton,
}: {
  title: string;
  description?: string;
  objectId: string;
  objectType: AclObjectType;
  objectName: string;
  userOrGroupLabel: string;
  userOrGroupId: string;
  userObjectType: UserObjectType;
  onSetPermissions: VoidFunction;
  showRemoveButton?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();

  const [isRemoving, setIsRemoving] = useState(false);

  return (
    <div className="flex items-center gap-2 border-t py-2">
      <div className="flex-1 text-sm">
        {title}
        {description && (
          <span className="block text-xs text-primary-600">{description}</span>
        )}
      </div>
      <Dialog open={open} onOpenChange={setOpen}>
        {open && (
          <>
            {objectType === "project" ? (
              <ProjectLevelPermissionsDialog
                projectId={objectId}
                projectName={objectName}
                userOrGroupLabel={userOrGroupLabel}
                userOrGroupId={userOrGroupId}
                userObjectType={userObjectType}
                onSubmit={() => {
                  setOpen(false);
                  onSetPermissions();
                }}
              />
            ) : (
              <ObjectLevelPermissionsDialog
                objectId={objectId}
                objectType={objectType}
                objectName={objectName}
                userOrGroupLabel={userOrGroupLabel}
                userOrGroupId={userOrGroupId}
                userObjectType={userObjectType}
                onSubmit={() => {
                  setOpen(false);
                  onSetPermissions();
                }}
              />
            )}
          </>
        )}

        <Tooltip>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <Button className="size-8 flex-none p-0">
                <Edit3Icon className="size-3" />
              </Button>
            </DialogTrigger>
          </TooltipTrigger>
          <TooltipContent>Edit access</TooltipContent>
        </Tooltip>
      </Dialog>
      {showRemoveButton && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              isLoading={isRemoving}
              className="size-8 flex-none p-0"
              onClick={async () => {
                setIsRemoving(true);

                try {
                  await onRemoveAccess({
                    objectId,
                    objectType,
                    userObjectType,
                    userOrGroupId,
                    getOrRefreshToken,
                    apiUrl: org.api_url,
                  });

                  await onSetPermissions();
                } catch (e) {
                  toast.error(
                    `Failed to remove access for ${userOrGroupLabel}`,
                  );
                  console.error(e);
                } finally {
                  setIsRemoving(false);
                }

                toast.success(
                  `Access to ${objectName} removed for ${userOrGroupLabel}`,
                );
              }}
            >
              <Trash2Icon className="size-3" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Remove access</TooltipContent>
        </Tooltip>
      )}
    </div>
  );
};
