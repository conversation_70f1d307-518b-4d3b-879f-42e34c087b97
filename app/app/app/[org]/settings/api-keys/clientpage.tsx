"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { Button } from "#/ui/button";
import { Loading } from "#/ui/loading";
import { useMemo, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "#/ui/dialog";
import { PlainInput } from "#/ui/plain-input";
import { Spinner } from "#/ui/icons/spinner";
import { createApiKey } from "./api-key";
import { isEmpty } from "#/utils/object";
import { AlertTriangle, Trash2Icon } from "lucide-react";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { smartTimeFormat } from "#/ui/date";
import {
  type deleteApiKeyAsOrgOwner,
  type fetchApiKeysAsOrgOwner,
  type deleteApiKey,
  type fetchApi<PERSON>eys,
} from "./actions";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "#/ui/tooltip";
import { type Organization } from "@braintrust/core/typespecs";
import { InfoBanner } from "#/ui/info-banner";
import { CreatedApiKeyDialog } from "./created-api-key-dialog";

function YourAPIKeys({ org }: { org: Organization }) {
  const { getToken } = useAuth();

  const [pendingKeyToDelete, setPendingKeyToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const { data: all_api_keys, invalidate: refreshApiKeys } = useQueryFunc<
    typeof fetchApiKeys
  >({
    fName: "fetchApiKeys",
    args: {},
  });
  const api_keys = useMemo(
    () =>
      all_api_keys?.filter((k) => isEmpty(k.org_id) || k.org_id === org.id) ??
      [],
    [all_api_keys, org.id],
  );

  const [lastCreatedKey, setLastCreatedKey] = useState<string | null>(null);

  const deleteKey = async (id: string) => {
    try {
      await invokeServerAction<typeof deleteApiKey>({
        fName: "deleteApiKey",
        args: { api_key_id: id },
        getToken,
      });
      refreshApiKeys();
    } catch (error) {
      toast.error("Failed to delete API key", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    }
  };

  const createKey = async (name: string, orgId: string) => {
    const key = await createApiKey({ name, orgId });
    setLastCreatedKey(key);
    refreshApiKeys();
    return { key };
  };

  return (
    <div>
      <h2 className="text-lg font-semibold">Your API keys</h2>
      {api_keys === null ? (
        <Loading />
      ) : (
        <>
          <Table className="mt-6 table-auto text-left">
            <TableHeader>
              <TableRow>
                <TableHead className="w-40">Name</TableHead>
                <TableHead className="w-32">Key</TableHead>
                <TableHead className="w-48">Scope</TableHead>
                <TableHead className="w-48">Created</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {api_keys.map((row) => (
                <TableRow key={row.id} className="py-2">
                  <BasicTooltip tooltipContent={row.name}>
                    <TableCell className="w-40">
                      <span className="truncate">{row.name}</span>
                    </TableCell>
                  </BasicTooltip>
                  <TableCell className="w-32 font-mono text-primary-700">
                    {row.preview_name.split("-").join("-*****")}
                  </TableCell>
                  <TableCell className="w-48">
                    {isEmpty(row.org_id) ? (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="flex items-center">
                            All organizations
                            <AlertTriangle className="ml-2 size-4 text-amber-600" />
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="max-h-48 max-w-md overflow-y-auto whitespace-pre-line break-words p-2 text-xs">
                            This is a legacy API key that grants access to all
                            of your organizations. Consider deleting it and
                            replacing it with new API keys which will be
                            automatically restricted to your current
                            organization.
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <BasicTooltip tooltipContent={org.name}>
                        <span className="truncate">{org.name}</span>
                      </BasicTooltip>
                    )}
                  </TableCell>
                  <TableCell className="w-48">
                    {row.created ? (
                      <time dateTime={row.created} title={row.created}>
                        {smartTimeFormat(new Date(row.created).getTime())}
                      </time>
                    ) : (
                      <div> unknown </div>
                    )}
                  </TableCell>
                  <TableCell className="flex flex-1 items-center justify-end pr-0">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="size-6"
                      onClick={(e) => {
                        setPendingKeyToDelete({ id: row.id, name: row.name });
                      }}
                    >
                      <Trash2Icon className="size-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              {pendingKeyToDelete && (
                <ConfirmationDialog
                  title="Confirm delete API key"
                  description={`Are you sure you want to delete the '${pendingKeyToDelete.name}' API key? This action cannot be undone.`}
                  open={Boolean(pendingKeyToDelete)}
                  onOpenChange={(isOpen) => {
                    if (!isOpen) {
                      setPendingKeyToDelete(null);
                    }
                  }}
                  onConfirm={() => {
                    deleteKey(pendingKeyToDelete.id);
                  }}
                  confirmText="Confirm"
                />
              )}
            </TableBody>
          </Table>
          <div className="mt-4">
            <CreateNewKeyModal
              onCreate={async (name) => {
                if (!org.id) {
                  toast.error("Failed to create API key", {
                    description: "No org ID",
                  });
                  return null;
                }
                return await createKey(name, org.id);
              }}
            />
          </div>
          <CreatedApiKeyDialog
            lastCreatedKey={lastCreatedKey}
            setLastCreatedKey={setLastCreatedKey}
          />
        </>
      )}
    </div>
  );
}

function CreateNewKeyModal({
  onCreate,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  onCreate: (name: string) => Promise<any>;
}) {
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string>("");
  const [opened, setOpened] = useState(false);

  return (
    <Dialog open={opened} onOpenChange={setOpened}>
      <DialogTrigger asChild>
        <Button>Create new API key</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-xl">
        <form
          onSubmit={async (e) => {
            e.preventDefault();

            setCreating(true);
            setError("");

            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            const formData = new FormData(e.target as HTMLFormElement);
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            const keyName = formData.get("apiKeyName") as string;

            try {
              const { key } = await onCreate(keyName || "New API key");
              setCreating(false);
              setOpened(false);
              return key;
            } catch (e) {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              setError((e as Error).message || "Failed to create API key");
              console.error("Failed to create API key", e);
              setCreating(false);
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>Create new API key</DialogTitle>
            <DialogDescription asChild>
              <div>
                <p className="mt-6">
                  Enter a name for your new API key. This name will be used to
                  identify the key in your account.
                </p>
              </div>
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="apiKeyName" className="text-right">
                API key name
              </label>
              <PlainInput
                id="apiKeyName"
                name="apiKeyName"
                className="col-span-3"
              />
            </div>
          </div>
          {error && <div className="mt-4 text-red-500">Error: {error}</div>}
          <DialogFooter>
            <Button>
              {creating ? (
                <div className="flex items-center">
                  Creating <Spinner className="ml-1" />
                </div>
              ) : (
                "Create"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

const OrgAPIKeys = ({ org }: { org: Organization }) => {
  const { getToken } = useAuth();

  const [pendingKeyToDelete, setPendingKeyToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const { data: all_api_keys, invalidate: refreshApiKeys } = useQueryFunc<
    typeof fetchApiKeysAsOrgOwner
  >({
    fName: "fetchApiKeysAsOrgOwner",
    args: { org_id: org.id ?? "" },
  });
  const api_keys = useMemo(
    () =>
      // NOTE: we intentionally do not show unscoped (org_id === null) keys here since
      // they could be used in other organizations.
      // This is a legacy edge case that does not apply to keys created in 2024 and later
      // when organization scoped api keys became the default in this UI:
      // https://github.com/braintrustdata/braintrust/commit/ece930e810944b3ae7b91d7537dfc038b65a05e5
      all_api_keys?.filter((k) => k.org_id === org.id) ?? [],
    [all_api_keys, org.id],
  );

  const deleteKey = async (id: string) => {
    try {
      await invokeServerAction<typeof deleteApiKeyAsOrgOwner>({
        fName: "deleteApiKeyAsOrgOwner",
        args: { api_key_id: id, org_id: org.id ?? "" },
        getToken,
      });
      refreshApiKeys();
    } catch (error) {
      toast.error("Failed to delete API key", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    }
  };

  return (
    <div>
      <h2 className="text-lg font-semibold">Organization API keys</h2>
      <InfoBanner className="mt-4">
        <p>
          As an organization owner, you can view and manage all API keys for
          your organization.
        </p>
      </InfoBanner>
      {api_keys === null ? (
        <Loading />
      ) : (
        <>
          <Table className="mt-6 table-auto text-left">
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Owner</TableHead>
                <TableHead className="w-40">Name</TableHead>
                <TableHead className="w-32">Key</TableHead>
                <TableHead className="w-40">Scope</TableHead>
                <TableHead className="w-48">Created</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {api_keys.map((row) => (
                <TableRow key={row.id} className="py-2">
                  <TableCell className="w-32 truncate">
                    <BasicTooltip tooltipContent={row.user_email}>
                      <span className="truncate">
                        {`${row.user_given_name || ""} ${row.user_family_name || ""}`.trim() ||
                          row.user_email ||
                          "unknown"}
                      </span>
                    </BasicTooltip>
                  </TableCell>
                  <BasicTooltip tooltipContent={row.name}>
                    <TableCell className="w-40">
                      <span className="truncate">{row.name}</span>
                    </TableCell>
                  </BasicTooltip>
                  <TableCell className="w-32 font-mono text-primary-700">
                    {row.preview_name.split("-").join("-*****")}
                  </TableCell>
                  <TableCell className="w-40">
                    {isEmpty(row.org_id) ? "All organizations" : org.name}
                  </TableCell>
                  <TableCell className="w-48">
                    {row.created ? (
                      <time dateTime={row.created} title={row.created}>
                        {smartTimeFormat(new Date(row.created).getTime())}
                      </time>
                    ) : (
                      <div> unknown </div>
                    )}
                  </TableCell>
                  <TableCell className="flex flex-1 items-center justify-end pr-0">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="size-6"
                      onClick={(e) => {
                        setPendingKeyToDelete({ id: row.id, name: row.name });
                      }}
                    >
                      <Trash2Icon className="size-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              {pendingKeyToDelete && (
                <ConfirmationDialog
                  title="Confirm delete API key"
                  description={`Are you sure you want to delete the '${pendingKeyToDelete.name}' API key? This action cannot be undone.`}
                  open={Boolean(pendingKeyToDelete)}
                  onOpenChange={(isOpen) => {
                    if (!isOpen) {
                      setPendingKeyToDelete(null);
                    }
                  }}
                  onConfirm={() => {
                    deleteKey(pendingKeyToDelete.id);
                  }}
                  confirmText="Confirm"
                />
              )}
            </TableBody>
          </Table>
        </>
      )}
    </div>
  );
};

export default function ClientPage({
  org,
  isOrgOwner,
}: {
  org: Organization;
  isOrgOwner: boolean;
}) {
  return (
    <div>
      <YourAPIKeys org={org} />

      {isOrgOwner && (
        <>
          <div className="my-12"></div>
          <OrgAPIKeys org={org} />
        </>
      )}
    </div>
  );
}
