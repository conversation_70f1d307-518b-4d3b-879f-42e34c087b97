"use client";

import React, { useState } from "react";
import { Input } from "#/ui/input";
import { Plus, Search, Server, User2, XIcon } from "lucide-react";

import { addMemberToGroup, removeMemberFromGroup } from "./api-groups";
import { getDisplayName, isServiceAccountEmail, useOrg } from "#/utils/user";
import { type getGroupMembers } from "./group-actions";
import { DialogContent, DialogHeader, DialogTitle } from "#/ui/dialog";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { Button } from "#/ui/button";
import { Spinner } from "#/ui/icons/spinner";
import { cn } from "#/utils/classnames";
import { useSessionToken } from "#/utils/auth/session-token";

export const GroupMembersDialog = ({
  name,
  orgName,
}: {
  name: string;
  orgName: string;
}) => {
  const { data, isLoading, invalidate } = useQueryFunc<typeof getGroupMembers>({
    fName: "getGroupMembers",
    args: { org_name: orgName, group_name: name },
  });

  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const [userIdBeingModifed, setUserIdBeingModifed] = useState<string | null>(
    null,
  );
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };

  const users = data?.users ?? [];
  const groupId = data?.groupId ?? "";
  const [memberUsers, inheritedMembers, nonMembers] = getUsersToDisplay(
    users,
    searchQuery,
  );

  const showMemberUsers = memberUsers.length > 0;
  const showInheritedMemberUsers = inheritedMembers.length > 0;
  const showNonMemberUsers = nonMembers.length > 0;

  const showEmptyMessage = !showMemberUsers && !showInheritedMemberUsers;

  const shouldDimTables = userIdBeingModifed !== null;

  async function modifyGroupMembership({
    user,
    addOrRemove,
  }: {
    user: User;
    addOrRemove: "add" | "remove";
  }) {
    const toastTitle =
      addOrRemove === "add"
        ? `${getDisplayName(user)} added to ${name}`
        : `${getDisplayName(user)} removed from ${name}`;

    const sessionToken = await getOrRefreshToken();

    const apiArgs = {
      userId: user.id,
      groupId,
      apiUrl,
      sessionToken,
    };

    setUserIdBeingModifed(user.id);

    try {
      if (addOrRemove === "add") {
        await addMemberToGroup(apiArgs);
      } else if (addOrRemove === "remove") {
        await removeMemberFromGroup(apiArgs);
      }
      invalidate();
      setSearchQuery("");
      toast(toastTitle);
    } catch (e) {
      toast.error("Something went wrong modifying the group's members", {
        description: e instanceof Error ? e.message : String(e),
      });
      console.error(e);
    } finally {
      setUserIdBeingModifed(null);
    }
  }

  return (
    <DialogContent className="block h-[80vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle>{name} permission group members</DialogTitle>
      </DialogHeader>
      {isLoading && <Spinner />}
      {data && (
        <div className="flex h-full flex-col">
          <div className="relative mt-4">
            <Search className="pointer-events-none absolute left-2 top-3 size-3 text-primary-500" />
            <Input
              placeholder="Find permission group member"
              className="h-9 flex-1 py-1 pl-6"
              onChange={handleSearchChange}
              value={searchQuery}
            />
          </div>
          {showEmptyMessage && (
            <div className="pt-4 text-sm text-primary-500">
              {searchQuery
                ? "No members found"
                : "There are no members in this permission group yet"}
            </div>
          )}
          <div className="-mx-6 flex-1 overflow-y-auto px-6">
            {showMemberUsers && (
              <div
                className={cn({
                  "opacity-50": shouldDimTables,
                })}
              >
                <SectionHeading>Group members</SectionHeading>
                {memberUsers.map((user) => {
                  return (
                    <UserItem key={user.id} user={user}>
                      <Button
                        title="Remove user from group"
                        size="xs"
                        variant="ghost"
                        isLoading={userIdBeingModifed === user.id}
                        Icon={XIcon}
                        onClick={() => {
                          modifyGroupMembership({
                            user,
                            addOrRemove: "remove",
                          });
                        }}
                      />
                    </UserItem>
                  );
                })}
              </div>
            )}
            {showInheritedMemberUsers && (
              <div
                className={cn({
                  "opacity-50": shouldDimTables,
                })}
              >
                <SectionHeading>Users with inherited membership</SectionHeading>
                {inheritedMembers.map((user) => {
                  return <UserItem key={user.id} user={user} />;
                })}
              </div>
            )}
            {showNonMemberUsers && (
              <div
                className={cn({
                  "opacity-50": shouldDimTables,
                })}
              >
                <SectionHeading>Non-members</SectionHeading>
                {nonMembers.map((user) => {
                  return (
                    <UserItem key={user.id} user={user}>
                      <Button
                        title="Add user to group"
                        size="xs"
                        variant="primary"
                        isLoading={userIdBeingModifed === user.id}
                        Icon={Plus}
                        onClick={() => {
                          modifyGroupMembership({
                            user,
                            addOrRemove: "add",
                          });
                        }}
                      />
                    </UserItem>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}
    </DialogContent>
  );
};

const UserItem = ({
  user,
  children,
}: React.PropsWithChildren<{
  user: User;
}>) => {
  const title = getDisplayName(user);
  return (
    <div className="flex items-center gap-2 border-t py-2">
      <div className="flex-1 text-sm">
        <div className="flex items-center gap-2">
          {isServiceAccountEmail(user.email) ? (
            <Server className="size-4" />
          ) : (
            <User2 className="size-4" />
          )}
          <span>{title}</span>
        </div>
      </div>
      {children}
    </div>
  );
};

interface User {
  id: string;
  given_name?: string | null;
  family_name?: string | null;
  email?: string | null;
  is_group_member: boolean;
  is_inherited_group_member: boolean;
}

function isSearchMatch(user: User, searchQuery: string) {
  if (!searchQuery) return true;

  const lowerQuery = searchQuery.toLowerCase();
  const lowerGivenName = user?.given_name?.toLowerCase() ?? "";
  const lowerFamilyName = user?.family_name?.toLowerCase() ?? "";
  const lowerEmail = user?.email?.toLowerCase() ?? "";
  const lowerFullName = `${lowerGivenName} ${lowerFamilyName}`;

  return lowerFullName.includes(lowerQuery) || lowerEmail.includes(lowerQuery);
}

function getUsersToDisplay(users: User[], searchQuery: string) {
  const memberUsers: User[] = [];
  const inheritedMembers: User[] = [];
  const nonMembers: User[] = [];

  users.forEach((user) => {
    if (isSearchMatch(user, searchQuery)) {
      if (user.is_group_member) {
        memberUsers.push(user);
      } else if (user.is_inherited_group_member) {
        inheritedMembers.push(user);
      } else {
        nonMembers.push(user);
      }
    }
  });

  return [memberUsers, inheritedMembers, nonMembers];
}

const SectionHeading = ({ children }: React.PropsWithChildren<{}>) => (
  <h3 className="mb-2 pt-6 text-sm font-medium">{children}</h3>
);
