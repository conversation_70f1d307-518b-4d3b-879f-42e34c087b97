"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { PlainInput } from "#/ui/plain-input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { useOrg } from "#/utils/user";
import Fuse, { type FuseResult } from "fuse.js";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type removeMembers } from "#/pages/api/organization/member_actions";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "#/ui/dialog";
import { smartTimeFormat } from "#/ui/date";
import { Trash2, UserCog } from "lucide-react";
import { Avatar } from "#/ui/avatar";
import OrgLevelPermissionsDialog from "#/ui/permissions/org-level-permissions-dialog";
import { type fetchOrgUsers } from "#/utils/org-users";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { ExternalLink } from "#/ui/link";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { type Permission } from "@braintrust/core/typespecs";
import { useAuth } from "@clerk/nextjs";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import {
  addUsersToOrg,
  InviteMembersForm,
  useInviteMembersForm,
  extractGmailStyleEmail,
} from "./invite-members-form";

type Member = {
  fullName: string;
  email: string;
  avatarURL: string;
  joinedAt: string;
  userId: string;
};

async function removeUserFromOrg({
  orgId,
  orgName,
  userId,
  email,
  refresh,
  getToken,
}: {
  orgId: string | undefined;
  orgName: string;
  userId: string;
  email: string;
  refresh: () => void;
  getToken: () => Promise<string | null>;
}) {
  if (!(orgId && orgName && userId && email)) return;
  try {
    await invokeServerAction<typeof removeMembers>({
      fName: "removeMembers",
      args: { orgId, users: { ids: [userId] } },
      getToken,
    });
    toast.success(`${email} removed`);
    refresh();
  } catch (error) {
    toast.error(`Could not remove user`, { description: `${error}` });
  }
}

export default function Page({
  orgPermissions,
  orgMemberPermissions,
}: {
  orgPermissions: Permission[];
  orgMemberPermissions: Permission[];
}) {
  const org = useOrg();
  const orgId = org.id;
  const orgName = org.name;

  const [addMemberDialogOpened, setAddMemberDialogOpened] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [initialAddMemberDialogText, setInitialAddMemberDialogText] =
    useState("");
  const searchMembersInputRef = useRef<HTMLInputElement>(null);
  const [debouncedSearchString, setDebouncedSearchString] =
    useState<string>("");

  const [memberToSetPermissions, setMemberToSetPermissions] =
    useState<Member | null>(null);

  const { data: orgUsers, invalidate: refreshMembersView } = useQueryFunc<
    typeof fetchOrgUsers
  >({
    fName: "fetchOrgUsers",
    args: { orgName, userType: "user" },
  });
  const membersView = useMemo(
    () => (orgUsers ? Object.values(orgUsers) : []),
    [orgUsers],
  );

  const isAllowedToReadAcls = orgPermissions?.includes("read_acls");
  const isAllowedToRemoveMembers = orgMemberPermissions?.includes("delete");
  const isAllowedToInviteMembers = orgMemberPermissions?.includes("create");

  const orgMembers = useMemo(() => {
    if (!membersView) return [];
    return membersView.map((member) => {
      return {
        fullName:
          member.given_name || member.family_name
            ? [member.given_name, member.family_name].join(" ")
            : null,
        email: member.email,
        avatarURL: member.avatar_url,
        joinedAt: member.created,
        userId: member.id,
      };
    });
  }, [membersView]);

  const fuse = useMemo(
    () =>
      new Fuse(orgMembers, {
        keys: ["fullName", "email"],
        threshold: 0.0,
        ignoreLocation: true,
        includeMatches: true,
      }),
    [orgMembers],
  );

  const filteredMembers = useMemo(() => {
    if (!debouncedSearchString) return orgMembers;
    const results = fuse.search(debouncedSearchString);
    return results.map((result) => result);
  }, [debouncedSearchString, fuse, orgMembers]);

  const setSearchDebounced = useDebouncedCallback(
    setDebouncedSearchString,
    500,
  );

  const findExistingEmail = useCallback(
    (text: string) => {
      const emailsToFind = text
        .split(",")
        .map((t) => t.trim())
        .map(extractGmailStyleEmail)
        .filter(Boolean);
      return emailsToFind.find((e) => membersView?.find((m) => m.email === e));
    },
    [membersView],
  );

  useEffect(() => {
    const input = searchMembersInputRef.current;
    if (!input) return;
    const inputHandler = (e: Event) => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      setSearchDebounced((e.target as HTMLInputElement).value);
    };
    input.addEventListener("input", inputHandler);
    return () => {
      input.removeEventListener("input", inputHandler);
    };
  }, [setSearchDebounced]);

  const { getToken } = useAuth();

  const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
  return (
    <>
      <div className="flex items-center">
        <PlainInput
          placeholder="Find members"
          className="flex-1"
          ref={searchMembersInputRef}
        />
        {isAllowedToInviteMembers && (
          <Button
            className="ml-4"
            onClick={() => {
              if (debouncedSearchString) {
                setInitialAddMemberDialogText(debouncedSearchString);
              }
              setAddMemberDialogOpened(true);
            }}
          >
            Invite member
          </Button>
        )}
      </div>
      <div className="mt-4">
        <ul role="list" className="divide-y divide-primary-100">
          {debouncedSearchString && filteredMembers.length == 0 && (
            <div className="flex flex-col items-center justify-center pt-6">
              <div className="flex items-center space-x-2">
                <div className="text-primary-500">
                  It looks like {debouncedSearchString} hasn&apos;t joined yet.
                </div>

                {emailRegex.test(debouncedSearchString) && (
                  <button
                    className="text-accent-500 focus:outline-none"
                    onClick={() => {
                      setInitialAddMemberDialogText(debouncedSearchString);
                      setAddMemberDialogOpened(true);
                    }}
                  >
                    Invite them!
                  </button>
                )}
              </div>
            </div>
          )}
          {filteredMembers.map((member, i) => {
            const item =
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              (member as FuseResult<Member>).item ?? (member as Member);

            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            const matches = (member as FuseResult<Member>).matches ?? [];

            const fullNameMatches = matches.filter(
              (match) => match.key === "fullName",
            );
            const emailMatches = matches.filter(
              (match) => match.key === "email",
            );
            const fullNameWithMatches =
              fullNameMatches?.length > 0
                ? getHighlightedMatches(fullNameMatches, debouncedSearchString)
                : item.fullName;
            const emailWithMatches =
              emailMatches?.length > 0
                ? getHighlightedMatches(emailMatches, debouncedSearchString)
                : item.email;

            return (
              <li
                key={item.email + i}
                className="group flex w-full items-center justify-between gap-x-6 rounded-md py-3"
              >
                <div className="flex items-center gap-x-4">
                  <Avatar imgUrl={item.avatarURL} size="md" />
                  <div className="min-w-0 flex-auto">
                    <p className="mb-0.5 text-sm font-semibold text-primary-700">
                      {fullNameWithMatches || emailWithMatches}
                    </p>
                    <p className="truncate text-xs text-primary-500">
                      {emailWithMatches}
                    </p>
                  </div>
                </div>
                <div className="hidden items-center gap-2 sm:flex">
                  <div className="text-xs text-primary-500">
                    Joined{" "}
                    <time dateTime={item.joinedAt} suppressHydrationWarning>
                      {smartTimeFormat(new Date(item.joinedAt).getTime())}
                    </time>
                  </div>
                  {isAllowedToRemoveMembers && (
                    <Button
                      size="icon"
                      className="size-8 p-0"
                      variant="border"
                      onClick={() => {
                        setMemberToDelete(item);
                      }}
                    >
                      <Trash2 className="size-4 text-primary-600" />
                    </Button>
                  )}
                  {isAllowedToReadAcls && (
                    <Button
                      className="size-8 p-0"
                      onClick={() => {
                        setMemberToSetPermissions(item);
                      }}
                    >
                      <UserCog className="size-4 text-primary-600" />
                    </Button>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
        {memberToDelete && (
          <ConfirmationDialog
            open={Boolean(memberToDelete)}
            onOpenChange={(isOpen) => {
              if (!isOpen) {
                setMemberToDelete(null);
              }
            }}
            title={
              <span className="leading-7">{`Are you sure you want to remove ${memberToDelete.email} from this organization?`}</span>
            }
            description="This user will no longer be able to access this organization"
            confirmText="Remove"
            onConfirm={() => {
              removeUserFromOrg({
                orgId,
                orgName,
                userId: memberToDelete.userId,
                email: memberToDelete.email,
                refresh: refreshMembersView,
                getToken,
              });
            }}
          />
        )}
        {memberToSetPermissions && (
          <Dialog
            open
            onOpenChange={(isOpen) => {
              if (!isOpen) {
                setMemberToSetPermissions(null);
              }
            }}
          >
            <OrgLevelPermissionsDialog
              orgId={orgId ?? ""}
              userOrGroupLabel={`${
                memberToSetPermissions.fullName || memberToSetPermissions.email
              }`}
              userOrGroupId={memberToSetPermissions.userId}
              userObjectType="user"
              onSubmit={() => {
                setMemberToSetPermissions(null);
              }}
            />
          </Dialog>
        )}
      </div>
      {addMemberDialogOpened && (
        <AddNewMember
          refreshMembersView={refreshMembersView}
          findExistingEmail={findExistingEmail}
          onClose={() => {
            setAddMemberDialogOpened(false);
            setInitialAddMemberDialogText("");
          }}
          initialInputText={initialAddMemberDialogText}
        />
      )}
    </>
  );
}

const getHighlightedMatches = (
  matches: FuseResult<Member>["matches"],
  searchValue: string,
) => {
  if (!matches) return "";
  return matches.reduce((acc, match) => {
    const { indices } = match;
    const { value } = match;
    if (!indices.length) return acc.concat(value);
    if (!value) return acc.concat(value);
    const length = value.length;
    const result = [];
    let lastEnd = 0;
    for (const [start, end] of indices) {
      result.push(value.slice(lastEnd, start));
      const currentMatch = value.slice(start, end + 1);
      if (currentMatch.length !== searchValue.length) {
        result.push(currentMatch);
      } else {
        result.push(
          <span className="bg-accent-100" key={currentMatch}>
            {currentMatch}
          </span>,
        );
      }
      lastEnd = end + 1;
    }
    result.push(value.slice(lastEnd, length));
    return acc.concat(result);
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  }, [] as React.ReactNode[]);
};

function AddNewMember({
  refreshMembersView,
  findExistingEmail,
  onClose,
  initialInputText = "",
}: {
  refreshMembersView: () => void;
  findExistingEmail: (user: string) => string | undefined;
  onClose: () => void;
  initialInputText?: string;
}) {
  const { id: orgId, name: orgName } = useOrg();
  const { getToken } = useAuth();

  const formState = useInviteMembersForm({
    findExistingEmail,
    initialInputText,
  });

  const handleClose = () => {
    onClose();
    formState.reset();
  };

  const handleSubmit = async () => {
    const emails = formState.getAllEmails();

    try {
      formState.setIsInviteInProgress(true);
      await addUsersToOrg({
        orgId,
        orgName,
        emails,
        groupIds: formState.selectedGroups.map((g) => g.groupId),
        refresh: refreshMembersView,
        getToken,
      });
    } finally {
      handleClose();
      formState.setIsInviteInProgress(false);
    }
  };

  return (
    <Dialog open onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="flex flex-col">
        <DialogHeader>
          <DialogTitle>
            Invite one or more members to this organization
          </DialogTitle>
          <DialogDescription asChild>
            <div>
              <p>
                Enter an email address to invite a member. To invite multiple
                members, separate email addresses with commas.
              </p>
              <p className="pt-2">
                To learn more about restricting access to specific projects or
                objects, read our{" "}
                <ExternalLink href="/docs/guides/access-control">
                  access control guide
                </ExternalLink>
                .
              </p>
            </div>
          </DialogDescription>
        </DialogHeader>

        <InviteMembersForm {...formState} orgName={orgName} />

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="ghost" onClick={onClose} size="sm">
              Cancel
            </Button>
          </DialogClose>
          <Button
            type="submit"
            variant="primary"
            size="sm"
            disabled={!formState.isFormValid()}
            onClick={handleSubmit}
            isLoading={formState.isInviteInProgress}
          >
            Invite
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
