"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { Button } from "#/ui/button";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Dialog } from "#/ui/dialog";
import { createServiceToken, provisionDataPlaneManager } from "./util";
import {
  AlertTriangle,
  CheckCircle,
  Ellipsis,
  PencilLine,
  PlusIcon,
  Server,
  Trash2Icon,
} from "lucide-react";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { smartTimeFormat } from "#/ui/date";
import {
  type deleteServiceTokenAsOrgOwner,
  type fetchServiceTokensAsOrgOwner,
} from "./actions";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import {
  type ApiKey,
  type User,
  type Organization,
} from "@braintrust/core/typespecs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { type removeMembers } from "#/pages/api/organization/member_actions";
import { type fetchOrgUsers } from "#/utils/org-users";

import OrgLevelPermissionsDialog from "#/ui/permissions/org-level-permissions-dialog";
import { CreateNewServiceTokenModal } from "./create-new-service-token-dialog";
import { CreateNewServiceAccountModal } from "./create-new-service-account-dialog";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { CreatedServiceTokenDialog } from "./created-service-token-dialog";
import { useFeatureFlags } from "#/lib/feature-flags";
import { isBraintrustDataPlane } from "#/utils/user-types";
import { InfoBanner } from "#/ui/info-banner";
import {
  type DataPlaneManager,
  type DataPlaneServiceToken,
} from "#/pages/api/service_token/provision_data_plane_manager";

export default function ClientPage({
  org,
  isSysadmin,
  isOrgOwner,
}: {
  org: Organization;
  isSysadmin: boolean;
  isOrgOwner: boolean;
}) {
  const { data: accountsById, invalidate: refreshAccounts } = useQueryFunc<
    typeof fetchOrgUsers
  >({
    fName: "fetchOrgUsers",
    args: { orgName: org.name, userType: "service_account" },
  });
  const accounts = useMemo(
    () => (accountsById ? Object.values(accountsById) : undefined),
    [accountsById],
  );

  const { getToken } = useAuth();
  const [lastCreatedToken, setLastCreatedToken] = useState<string | null>(null);

  const [tokenToDelete, setTokenToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [serviceaccountToRemove, setServiceAccountToRemove] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const [selectedAccount, setSelectedAccount] = useState<User | null>(null);

  const [serviceaccountToSetPermissions, setServiceAccountToSetPermissions] =
    useState<{
      id: string;
      name: string;
    } | null>(null);

  const deleteToken = async (id: string) => {
    try {
      await invokeServerAction<typeof deleteServiceTokenAsOrgOwner>({
        fName: "deleteServiceTokenAsOrgOwner",
        args: { service_token_id: id, org_id: org.id ?? "" },
        getToken,
      });
      refreshTokens();
    } catch (error) {
      toast.error("Failed to delete service token", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    }
  };

  const createToken = async (
    name: string,
    orgId: string,
    accountId: string,
  ) => {
    const token = await createServiceToken({
      name,
      orgId,
      accountId: accountId,
    });
    setLastCreatedToken(token);
    refreshTokens();
  };

  const removeServiceAccount = async (id: string) => {
    try {
      await invokeServerAction<typeof removeMembers>({
        fName: "removeMembers",
        args: { orgId: org.id ?? "", users: { ids: [id] } },
        getToken,
      });
      refreshAccounts();
    } catch (error) {
      toast.error("Failed to remove serviceaccount", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
      });
    }
  };

  const { data: all_service_tokens, invalidate: refreshTokens } = useQueryFunc<
    typeof fetchServiceTokensAsOrgOwner
  >({
    fName: "fetchServiceTokensAsOrgOwner",
    args: { org_id: org.id ?? "" },
  });
  const tokens = useMemo(
    () =>
      // NOTE: we intentionally do not show unscoped (org_id === null) keys here since
      // they could be used in other organizations.
      // This is a legacy edge case that does not apply to keys created in 2024 and later
      // when organization scoped api keys became the default in this UI:
      // https://github.com/braintrustdata/braintrust/commit/ece930e810944b3ae7b91d7537dfc038b65a05e5
      all_service_tokens?.filter((k) => k.org_id === org.id) ?? [],
    [all_service_tokens, org.id],
  );

  const { flags } = useFeatureFlags();

  if (!flags.serviceAccounts || !isOrgOwner) {
    return (
      <TableEmptyState label="You do not have permission to manage service tokens for this organization. Ask your administrator to grant the 'Manage settings' permission for this organization." />
    );
  }

  const dataPlaneManager = isSysadmin &&
    org.api_url &&
    isBraintrustDataPlane(org.api_url) && (
      <div className="mt-12">
        <DataPlaneManagerCard
          orgId={org.id}
          apiUrl={org.api_url}
          getToken={getToken}
        />
      </div>
    );

  if (accounts && accounts.length === 0) {
    return (
      <div>
        <h3 className="mb-6 text-lg font-semibold">Service tokens</h3>
        <div className="flex flex-col items-center justify-center gap-4 rounded-md border p-4 bg-primary-50 border-primary-100">
          <Server className="text-primary-400" />
          <p className="text-base text-primary-500">
            No service tokens and accounts found
          </p>
          <CreateNewServiceAccountModal
            variant="border"
            org={org}
            refreshAccounts={refreshAccounts}
            refreshTokens={refreshTokens}
            setLastCreatedToken={setLastCreatedToken}
            accounts={accounts}
          />
        </div>
        {dataPlaneManager}
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold">Service tokens</h3>
        <CreateNewServiceAccountModal
          org={org}
          refreshAccounts={refreshAccounts}
          refreshTokens={refreshTokens}
          setLastCreatedToken={setLastCreatedToken}
          accounts={accounts}
        />
      </div>
      <div className="flex flex-col gap-2">
        {accounts && accounts.length > 0 ? (
          accounts.map((account) => (
            <ServiceAccountCard
              key={account.id}
              account={account}
              tokens={tokens.filter((token) => token.user_id === account.id)}
              setSelectedAccount={setSelectedAccount}
              setTokenToDelete={setTokenToDelete}
              setServiceAccountToRemove={setServiceAccountToRemove}
              setServiceAccountToSetPermissions={
                setServiceAccountToSetPermissions
              }
            />
          ))
        ) : (
          <div className="mt-6 text-center text-sm text-primary-500">
            No service accounts found
          </div>
        )}
      </div>

      <CreateNewServiceTokenModal
        isOpen={selectedAccount !== null}
        selectedAccount={selectedAccount}
        setSelectedAccount={setSelectedAccount}
        onCreate={async (name, account) => {
          if (!org.id) {
            toast.error("Failed to create service token", {
              description: "No org ID",
            });
            return;
          }
          await createToken(name, org.id, account.id);
        }}
      />

      <ConfirmationDialog
        title="Confirm delete API key"
        description={`Are you sure you want to delete the '${tokenToDelete?.name}' service token? This action cannot be undone.`}
        open={Boolean(tokenToDelete)}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setTokenToDelete(null);
          }
        }}
        onConfirm={() => {
          if (tokenToDelete) {
            deleteToken(tokenToDelete.id);
          }
        }}
        confirmText="Confirm"
      />
      <ConfirmationDialog
        title="Confirm delete service account"
        description={`Are you sure you want to delete the service account '${serviceaccountToRemove?.name}'? This action cannot be undone.`}
        open={Boolean(serviceaccountToRemove)}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setServiceAccountToRemove(null);
          }
        }}
        onConfirm={() => {
          if (serviceaccountToRemove) {
            removeServiceAccount(serviceaccountToRemove.id);
          }
        }}
        confirmText="Confirm"
      />
      {serviceaccountToSetPermissions && (
        <Dialog
          open
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setServiceAccountToSetPermissions(null);
            }
          }}
        >
          <OrgLevelPermissionsDialog
            orgId={org.id ?? ""}
            userOrGroupLabel={`${serviceaccountToSetPermissions.name}`}
            userOrGroupId={serviceaccountToSetPermissions.id}
            userObjectType="user"
            onSubmit={() => {
              setServiceAccountToSetPermissions(null);
            }}
          />
        </Dialog>
      )}
      <CreatedServiceTokenDialog
        lastCreatedToken={lastCreatedToken}
        setLastCreatedToken={setLastCreatedToken}
      />
      {dataPlaneManager}
    </div>
  );
}

const ServiceAccountCard = ({
  account,
  tokens,
  setSelectedAccount,
  setTokenToDelete,
  setServiceAccountToRemove,
  setServiceAccountToSetPermissions,
}: {
  account: User;
  tokens: Array<ApiKey> | undefined;
  setSelectedAccount: (account: User | null) => void;
  setTokenToDelete: (token: { id: string; name: string }) => void;
  setServiceAccountToRemove: (serviceaccount: {
    id: string;
    name: string;
  }) => void;
  setServiceAccountToSetPermissions: (serviceaccount: {
    id: string;
    name: string;
  }) => void;
}) => {
  return (
    <div className="flex flex-col gap-2 rounded-md border p-2">
      <div className="flex flex-col">
        <div className="flex items-center justify-between">
          <div className="flex w-full items-start justify-between gap-2">
            <div className="flex flex-col">
              <div className="text-sm font-medium">{account.given_name}</div>
              <div className="font-mono text-xs text-primary-500">
                Service account
              </div>
            </div>
            <CopyToClipboardButton
              textToCopy={account.id}
              size={"xs"}
              variant={"ghost"}
              className="ml-auto mt-1 size-fit flex-none gap-2 px-0 font-mono text-xs transition-all text-primary-500 hover:bg-transparent hover:text-primary-900"
            >
              {account.id}
            </CopyToClipboardButton>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="icon" variant="ghost" className="size-6">
                  <Ellipsis className="size-4 text-primary-700" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setServiceAccountToSetPermissions({
                      id: account.id,
                      name: account.given_name || "unknown",
                    });
                  }}
                >
                  <PencilLine className="size-3" />
                  Edit Permissions
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedAccount(account);
                  }}
                >
                  <PlusIcon className="size-3" />
                  Add token
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setServiceAccountToRemove({
                      id: account.id,
                      name: account.given_name ?? "",
                    });
                  }}
                >
                  <Trash2Icon className="size-3" />
                  Delete service account
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-0 pt-1"></div>
      <Table className="table-auto text-left">
        <TableHeader>
          <TableRow className="text-xs">
            <TableHead className="w-60 truncate">Service token name</TableHead>
            <TableHead className="flex-1 truncate">Key</TableHead>
            <TableHead className="w-48">Created</TableHead>
            <TableHead className="w-6"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tokens && tokens.length > 0 ? (
            tokens?.map((token) => (
              <TableRow
                key={token.id}
                className="border-none py-1 text-sm text-primary-700"
              >
                <TableCell className="w-60 truncate">{token.name}</TableCell>
                <TableCell className="flex-1 truncate font-mono">
                  {token.preview_name?.replace(/^(bt-st-)(.{4})$/, "$1****$2")}
                </TableCell>
                <TableCell className="w-48">
                  {smartTimeFormat(new Date(token.created ?? "").getTime())}
                </TableCell>
                <TableCell className="w-fit pr-0">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="size-6"
                    onClick={() => {
                      setTokenToDelete({ id: token.id, name: token.name });
                    }}
                  >
                    <Trash2Icon className="size-4 text-primary-500" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow className="py-4 text-center text-sm text-primary-500">
              <TableCell
                colSpan={4}
                className="flex-1 items-center justify-center py-0 text-center text-sm text-primary-500"
              >
                No service tokens found for this service account
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

const DataPlaneManagerCard = ({
  orgId,
  apiUrl,
  getToken,
}: {
  orgId: string;
  apiUrl: string;
  getToken: () => Promise<string | null>;
}) => {
  const [provisionResponse, setProvisionResponse] = useState<{
    data_plane_manager: DataPlaneManager;
    data_plane_service_token: DataPlaneServiceToken;
    data_plane_has_service_token: boolean;
  } | null>(null);
  const [initialized, setInitialized] = useState(false);
  const [isProvisioning, setIsProvisioning] = useState(false);
  const [provisionError, setProvisionError] = useState<string | null>(null);

  const hasError = useMemo(() => {
    return (
      !isProvisioning &&
      (provisionError ||
        provisionResponse?.data_plane_has_service_token === false)
    );
  }, [provisionResponse, provisionError, isProvisioning]);

  const provision = useCallback(
    async (forceRecreateToken: boolean) => {
      setIsProvisioning(true);
      setProvisionError(null);
      try {
        const { data_plane_manager, data_plane_service_token } =
          await provisionDataPlaneManager({
            orgId,
            forceRecreateToken,
          });

        if (forceRecreateToken && !data_plane_service_token.key) {
          setProvisionResponse({
            data_plane_manager,
            data_plane_service_token,
            data_plane_has_service_token: false,
          });
          throw new Error("Failed to re-provision service token.");
        }

        const token = await getToken();
        if (!token) {
          setProvisionResponse({
            data_plane_manager,
            data_plane_service_token,
            data_plane_has_service_token: false,
          });
          throw new Error("No auth token found");
        }

        if (!data_plane_service_token.key) {
          const resp = await fetch(
            `${apiUrl}/service-token/${data_plane_service_token.name}`,
            {
              method: "HEAD",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            },
          );
          if (resp.status === 200) {
            setProvisionResponse({
              data_plane_manager,
              data_plane_service_token,
              data_plane_has_service_token: true,
            });
          } else {
            setProvisionResponse({
              data_plane_manager,
              data_plane_service_token,
              data_plane_has_service_token: false,
            });
          }
        } else {
          const resp = await fetch(`${apiUrl}/service-token/upsert`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              name: data_plane_service_token.name,
              service_token: data_plane_service_token.key,
            }),
          });

          if (!resp.ok) {
            setProvisionResponse({
              data_plane_manager,
              data_plane_service_token,
              data_plane_has_service_token: false,
            });
            throw new Error(
              `Failed to update data plane service token: ${await resp.text()}`,
            );
          }

          const data = await resp.json();
          if (!data.success) {
            setProvisionResponse({
              data_plane_manager,
              data_plane_service_token,
              data_plane_has_service_token: false,
            });
            throw new Error(
              `Failed to update data plane service token: ${data.error}`,
            );
          }

          setProvisionResponse({
            data_plane_manager,
            data_plane_service_token,
            data_plane_has_service_token: true,
          });
        }
      } catch (error) {
        setProvisionError(
          error instanceof Error
            ? error.message
            : "An unexpected error occurred, please try again",
        );
      } finally {
        setIsProvisioning(false);
      }
    },
    [apiUrl, orgId, getToken],
  );

  useEffect(() => {
    if (!initialized) {
      provision(false);
      setInitialized(true);
    }
  }, [provision, setInitialized, initialized]);

  return (
    <div>
      <InfoBanner>
        <p>
          As a sysadmin for the Braintrust data plane, you can manage the data
          plane manager service account and service tokens. Note that The data
          plane manager is a sysadmin service account with unscoped service
          tokens which allows it to administer functionality like retention
          across all orgs on this data plane.
        </p>
      </InfoBanner>
      <h2 className="mt-6 text-lg font-semibold">Data plane manager</h2>
      <div className="mt-6 flex flex-col gap-4">
        {isProvisioning && (
          <div className="text-sm text-primary-700">
            Loading data plane manager status...
          </div>
        )}
        {provisionResponse && !isProvisioning && (
          <div className="mb-4 text-sm">
            <p>
              <strong>ID:</strong>{" "}
              <code>{provisionResponse.data_plane_manager.id}</code>
            </p>
            <p>
              <strong>Name:</strong>{" "}
              <code>{provisionResponse.data_plane_manager.name}</code>
            </p>
          </div>
        )}
      </div>
      <h2 className="mt-6 text-lg font-semibold">Data plane service token</h2>
      <div className="mt-6 flex flex-col gap-4">
        {isProvisioning && (
          <div className="text-sm text-primary-700">
            Loading data plane service token status...
          </div>
        )}
        {provisionResponse && !isProvisioning && (
          <div className="mb-4 text-sm">
            <p>
              <strong>ID:</strong>{" "}
              <code>{provisionResponse.data_plane_service_token.id}</code>
            </p>
            <p>
              <strong>Name:</strong>{" "}
              <code>{provisionResponse.data_plane_service_token.name}</code>
            </p>
            <p>
              <strong>Token:</strong>{" "}
              <span>
                {provisionResponse.data_plane_service_token.key ? (
                  <>
                    <code>
                      bt-st-*****
                      {provisionResponse.data_plane_service_token.key.slice(-4)}
                    </code>
                    <CopyToClipboardButton
                      variant="ghost"
                      textToCopy={
                        provisionResponse.data_plane_service_token.key
                      }
                      className="size-6"
                    />
                  </>
                ) : (
                  <span>
                    Service token already provisioned. Reprovision to copy.
                  </span>
                )}
              </span>
            </p>

            {hasError ? (
              <>
                <div className="mt-4 flex flex-row items-center gap-2">
                  <AlertTriangle className="size-4 text-bad-500" />
                  <span className="text-sm text-bad-500">
                    Service token not saved to data plane. Reprovision to fix.
                  </span>
                </div>
                {provisionError && !isProvisioning && (
                  <div className="mt-4">
                    <span className="text-sm text-bad-500">
                      {provisionError}
                    </span>
                  </div>
                )}
              </>
            ) : (
              <div className="mt-4 flex flex-row items-center gap-2">
                <CheckCircle className="size-4 text-good-500" />
                <span className="text-sm">
                  Service token saved to data plane.
                </span>
              </div>
            )}
          </div>
        )}
      </div>
      <Button
        className="mt-2 w-64"
        isLoading={isProvisioning}
        disabled={isProvisioning}
        onClick={() => provision(true)}
      >
        {isProvisioning
          ? initialized
            ? "Provisioning..."
            : "Loading..."
          : "Force re-provision"}
      </Button>
    </div>
  );
};
