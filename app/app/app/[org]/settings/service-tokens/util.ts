import {
  type DataPlaneManager,
  type DataPlaneServiceToken,
} from "#/pages/api/service_token/provision_data_plane_manager";

export async function createServiceToken({
  name,
  orgId,
  accountId,
}: {
  name: string;
  orgId: string;
  accountId: string;
}) {
  const resp = await fetch("/api/service_token/register", {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ name, org_id: orgId, account_id: accountId }),
  });

  if (!resp.ok) {
    throw new Error(await resp.text());
  }

  const { service_token } = await resp.json();
  return service_token.key;
}

export async function provisionDataPlaneManager({
  orgId,
  forceRecreateToken,
}: {
  orgId: string;
  forceRecreateToken?: boolean;
}): Promise<{
  data_plane_manager: DataPlaneManager;
  data_plane_service_token: DataPlaneServiceToken;
}> {
  const resp = await fetch("/api/service_token/provision_data_plane_manager", {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      org_id: orgId,
      force_recreate_token: forceRecreateToken,
    }),
  });

  if (!resp.ok) {
    throw new Error(await resp.text());
  }

  return await resp.json();
}
