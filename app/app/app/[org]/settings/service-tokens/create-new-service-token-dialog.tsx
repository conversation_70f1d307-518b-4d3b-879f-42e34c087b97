import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "#/ui/dialog";
import { PlainInput } from "#/ui/plain-input";
import { type User } from "@braintrust/core/typespecs";
import { But<PERSON> } from "#/ui/button";
import { useState } from "react";
import { toast } from "sonner";
import { Label } from "#/ui/label";

export function CreateNewServiceTokenModal({
  onCreate,
  isOpen,
  selectedAccount,
  setSelectedAccount,
}: {
  onCreate: (name: string, account: User) => Promise<void>;
  isOpen: boolean;
  setSelectedAccount: (account: User | null) => void;
  selectedAccount: User | null;
}) {
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string>("");

  const [tokenName, setTokenName] = useState<string | null>(null);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          setSelectedAccount(null);
        }
      }}
    >
      <DialogContent className="sm:max-w-xl">
        <form
          onSubmit={async (e) => {
            e.preventDefault();
            if (!selectedAccount) {
              toast.error("Failed to create service token", {
                description: "No service account selected",
              });
              return;
            }
            if (!tokenName) {
              toast.error("Failed to create service token", {
                description: "No service token name provided",
              });
              return;
            }

            setCreating(true);
            setError("");

            try {
              await onCreate(tokenName, selectedAccount);
              setCreating(false);
              setSelectedAccount(null);
            } catch (e) {
              setError(
                (e instanceof Error ? e.message : `${e}`) ||
                  "Failed to create service token",
              );
              console.error("Failed to create service token", e);
              setCreating(false);
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>
              Create new service token for {selectedAccount?.given_name}
            </DialogTitle>
            <DialogDescription>
              Service accounts use service tokens to authenticate with
              Braintrust
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-2 py-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="account">Service account</Label>
              {selectedAccount && (
                <div className="flex h-10 items-center gap-2 rounded-md border px-2 py-1 text-sm bg-primary-50 text-gray-500">
                  {selectedAccount?.given_name}
                </div>
              )}
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="serviceTokenName">Service token name</Label>
              <PlainInput
                id="serviceTokenName"
                name="serviceTokenName"
                className="col-span-3"
                onChange={(e) => {
                  setTokenName(e.target.value);
                }}
              />
            </div>
          </div>
          {error && <div className="mt-4 text-red-500">Error: {error}</div>}
          <DialogFooter>
            <Button
              variant="primary"
              isLoading={creating}
              disabled={!tokenName || !selectedAccount}
            >
              Create
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
