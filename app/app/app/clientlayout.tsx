"use client";

import { Duck<PERSON><PERSON><PERSON>rovider } from "#/utils/duckdb";
import { useOrg, UserProviderServer } from "#/utils/user";
import { type UserContextT } from "#/utils/user-types";
import { type getUserContext } from "#/utils/user-context-action";
import {
  focusManager,
  onlineManager,
  isServer,
  QueryCache,
  QueryClient,
  QueryClientProvider,
  keepPreviousData,
} from "@tanstack/react-query";
import { useQueryFunc } from "#/utils/react-query";
import { toast } from "sonner";
import * as Sentry from "@sentry/nextjs";
import { useQueryState } from "nuqs";
import { useEffect, useMemo } from "react";
import { BtSessionTokenProvider } from "#/utils/auth/session-token";
import { useAuth } from "@clerk/nextjs";
import NavigationDetectionProvider from "#/utils/navigation-detection";
import { HypertuneProvider } from "#/generated/hypertune.react";
import { useUser } from "#/utils/user";
import { type CreateSourceOptions, type RootArgs } from "#/generated/hypertune";

// https://tanstack.com/query/v5/docs/framework/react/guides/advanced-ssr#initial-setup
function makeQueryClient() {
  return new QueryClient({
    queryCache: new QueryCache({
      onError: (err, query) => {
        // https://tkdodo.eu/blog/react-query-error-handling#the-global-callbacks
        if (query.meta?.disableGlobalErrorCallback) {
          return;
        }

        if (!query.meta?.disableGlobalErrorConsole) {
          console.warn("Failed to run query", err);
        }

        if (!query.meta?.disableGlobalErrorToast) {
          toast.error("Error", { description: err.message });
        }

        if (query.meta?.globalErrorSentryContext) {
          Sentry.captureException(err, query.meta.globalErrorSentryContext);
        }
      },
    }),
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
        retry: false,
        placeholderData: keepPreviousData,
      },
    },
  });
}

// Before we tell the focus manager and online manager that the page is alive, we need to make sure
// that we have a valid Clerk session token. This is necessary in the case of long
// dormant browser tabs that awaken with expired Clerk session tokens. We need all of the
// refetchOnWindowFocus and refetchOnReconnect queries to have a valid Clerk session token before they start.
function handleWithNewClerkSessionToken(
  value: boolean,
  getToken: () => Promise<string | null>,
  handler: (bool: boolean) => void,
) {
  return getToken().then((newToken) => {
    if (newToken) {
      handler(value);
    }
  });
}

function setupQueryManagers(getToken: () => Promise<string | null>) {
  //  Make focus refetching more similar to SWR's behavior:
  //  https://github.com/vercel/swr/blob/1585a3e37d90ad0df8097b099db38f1afb43c95d/src/_internal/utils/web-preset.ts#L29-L41
  //  This was removed from react-query in v5 but it should be fine for us to have here:
  //  https://github.com/TanStack/query/pull/4805
  //  If we end up using realtime more for updates we can change this in the future
  focusManager.setEventListener((handleFocus) => {
    if (typeof window !== "undefined" && window.addEventListener) {
      const visibilitychangeHandler = () => {
        handleWithNewClerkSessionToken(
          document.visibilityState === "visible",
          getToken,
          handleFocus,
        );
      };
      const focusHandler = (event: FocusEvent) => {
        handleWithNewClerkSessionToken(true, getToken, handleFocus);
      };
      const blurHandler = (event: FocusEvent) => {
        handleFocus(false);
      };
      window.addEventListener(
        "visibilitychange",
        visibilitychangeHandler,
        false,
      );
      window.addEventListener("focus", focusHandler, false);
      window.addEventListener("blur", blurHandler, false);
      return () => {
        window.removeEventListener("visibilitychange", visibilitychangeHandler);
        window.removeEventListener("focus", focusHandler);
        window.removeEventListener("blur", blurHandler, false);
      };
    }
  });

  onlineManager.setEventListener((handleOnline) => {
    if (typeof window !== "undefined" && window.addEventListener) {
      const onlineHandler = () => {
        handleWithNewClerkSessionToken(true, getToken, handleOnline);
      };
      const offlineHandler = () => {
        handleOnline(false);
      };
      window.addEventListener("online", onlineHandler, false);
      window.addEventListener("offline", offlineHandler, false);
      return () => {
        window.removeEventListener("online", onlineHandler);
        window.removeEventListener("offline", offlineHandler);
      };
    }
  });
}

let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient() {
  if (isServer) {
    // Server: always make a new query client
    return makeQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}

export function ClientLayout({
  children,
  userContext: userContextServer,
}: {
  children: React.ReactNode;
  userContext: UserContextT;
}) {
  // NOTE: Avoid useState when initializing the query client if you don't
  //       have a suspense boundary between this and the code that may
  //       suspend because React will throw away the client on the initial
  //       render if it suspends and there is no boundary
  const queryClient = getQueryClient();

  const {
    data: userContext,
    invalidate: refreshCtx,
    setData,
  } = useQueryFunc<typeof getUserContext>({
    fName: "getUserContext",
    args: {},
    serverData: userContextServer,
    queryClient,
  });

  const [resetUser, setResetUser] = useQueryState("reset-user");

  const { getToken } = useAuth();

  useEffect(() => {
    setupQueryManagers(getToken);
  }, [getToken]);

  useEffect(() => {
    if (Boolean(resetUser)) {
      // This flag is set after sign in, and instructs us to reset the user context,
      // without having to fully refresh the page. After resetting the user, we also
      // clear the other query caches, because they may have stale data (e.g. an empty
      // list of projects).
      queryClient.clear();
      setData(userContextServer);
      setResetUser(null);
    }
  }, [resetUser, userContextServer, queryClient, setData, setResetUser]);

  useEffect(() => {
    const queryCache = queryClient.getQueryCache();

    const unsubscribe = queryCache.subscribe((event) => {
      const { meta, state, queryKey } = event.query;
      if (
        event.type === "removed" &&
        meta?.onRemoveFromCache &&
        typeof meta.onRemoveFromCache === "function"
      ) {
        meta.onRemoveFromCache(state, queryKey);
      }
    });

    return unsubscribe;
  }, [queryClient]);

  return (
    <NavigationDetectionProvider>
      <QueryClientProvider client={queryClient}>
        <DuckDBProvider>
          <UserProviderServer ctx={userContext} refreshCtx={refreshCtx}>
            <HypertuneProviderWrapper>
              <BtSessionTokenProvider>{children}</BtSessionTokenProvider>
            </HypertuneProviderWrapper>
          </UserProviderServer>
        </DuckDBProvider>
        {/* uncomment to enable in dev */}
        {/*<ReactQueryDevtools initialIsOpen={false} />*/}
      </QueryClientProvider>
    </NavigationDetectionProvider>
  );
}

const hypertuneCreateSourceOptions: CreateSourceOptions = {
  token: process.env.NEXT_PUBLIC_HYPERTUNE_TOKEN!,
};

function HypertuneProviderWrapper({ children }: { children: React.ReactNode }) {
  const { user } = useUser();
  const org = useOrg();

  const args: RootArgs = useMemo(() => {
    return {
      context: {
        environment:
          process.env.NODE_ENV === "production" ? "production" : "development",
        user: {
          id: user?.id || "anonymous",
          email: user?.email || "none",
        },
        org: {
          id: org?.id || "none",
          name: org?.name || "none",
        },
      },
    };
  }, [user, org]);

  return (
    <HypertuneProvider
      createSourceOptions={hypertuneCreateSourceOptions}
      rootArgs={args}
    >
      {children}
    </HypertuneProvider>
  );
}
