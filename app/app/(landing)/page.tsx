import { redirect } from "next/navigation";

import { type <PERSON>adata } from "next";
import { getServerAuthSession } from "#/utils/auth/server-session";
import { Landing } from "./landing";
import { buildMetadata } from "../metadata";
import { StructuredData } from "#/ui/structured-data";
import {
  generateOrganizationSchema,
  generateWebSiteSchema,
  generateSoftwareApplicationSchema,
} from "#/lib/structured-data";

export default async function Page() {
  const session = await getServerAuthSession();

  if (session?.loggedIn) {
    return redirect("/app");
  }

  return (
    <>
      <StructuredData
        data={[
          generateOrganizationSchema(),
          generateWebSiteSchema(),
          generateSoftwareApplicationSchema(),
        ]}
      />
      <Landing />
    </>
  );
}

export const metadata: Metadata = buildMetadata({
  title: "Braintrust - Ship LLM products that work",
});
