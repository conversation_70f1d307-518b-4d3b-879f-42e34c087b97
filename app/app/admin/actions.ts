import { isAllowedSysadmin } from "#/utils/derive-error-context";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { type AuthLookup, HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { eventObjectType, projectSchema } from "@braintrust/core/typespecs";
import { backfillableObjectTypeSchema } from "@braintrust/local/app-schema";
import { signAsync } from "@noble/ed25519";
import { z } from "zod";
import Orb from "orb-billing";
import { orgContextSchema, type OrgContextT } from "#/utils/user-types";

export const orgZodSchema = z.object({
  id: z.string(),
  name: z.string(),
  created: z.string().datetime(),
  api_url: z.string().nullable(),
  span_limit: z.string().nullable().pipe(z.coerce.number().nullable()),
  log_bytes_limit: z.string().nullable().pipe(z.coerce.number().nullable()),
  brainstore_license_id: z.string().nullable(),
  telemetry_url: z.string().nullable(),
  plan_id: z.string().nullable(),
});

export type OrgRow = z.infer<typeof orgZodSchema>;

export const projectZodSchema = z.object({
  id: z.string(),
  org_id: z.string(),
  name: z.string(),
  created: z.string().datetime(),
});

export type ProjectRow = z.infer<typeof projectZodSchema>;

export const objectZodSchema = z.object({
  object_type: backfillableObjectTypeSchema,
  id: z.string(),
  object_id: z.string(),
  name: z.string(),
  created: z.string().datetime(),
  project_id: z.string(),
  deleted_at: z.string().datetime().nullable(),
});

export type ObjectRow = z.infer<typeof objectZodSchema>;

const objectOrOrgZodSchema = z.union([
  objectZodSchema,
  z.object({
    object_type: z.literal("org"),
    id: z.string(),
    name: z.string(),
    created: z.string().datetime(),
  }),
]);

const resourceCountSchema = z.array(
  z.object({
    resource_name: z.string(),
    date_bucket: z.coerce.date(),
    count: z.coerce.number(),
  }),
);

export type ResourceCount = z.infer<typeof resourceCountSchema>;

const adminProjectContextSchema = z.strictObject({
  id: projectSchema.shape.id,
});

export type AdminProjectContext = z.infer<typeof adminProjectContextSchema>;

export async function adminFetchResourceCounts(
  {
    orgId,
  }: {
    orgId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<ResourceCount> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();
  const { rows } = await db.query(
    `WITH config_setting AS (
      SELECT COALESCE(value_boolean, false) as use_months
      FROM app_config
      WHERE key = 'use_calendar_months'
      -- Ensure we get exactly one row, defaulting to false if the key doesn't exist
      UNION ALL SELECT false WHERE NOT EXISTS (SELECT 1 FROM app_config WHERE key = 'use_calendar_months')
      LIMIT 1
    ), latest_dates AS (
      SELECT rc.resource_name, MAX(rc.date_bucket) AS latest_date
      FROM resource_counts rc
      CROSS JOIN config_setting cs -- Get the config value
      WHERE rc.org_id = $1
      AND (
        (cs.use_months AND rc.resource_name LIKE '%_calendar_months') OR
        (NOT cs.use_months AND rc.resource_name NOT LIKE '%_calendar_months')
      )
      GROUP BY rc.resource_name
    )
    SELECT
      rc.resource_name,
      rc.date_bucket,
      SUM(rc.count) AS count
    FROM resource_counts rc
    INNER JOIN latest_dates ld
      ON rc.resource_name = ld.resource_name
      AND rc.date_bucket = ld.latest_date
    CROSS JOIN config_setting cs -- Get the config value again for the final select
    WHERE rc.org_id = $1
      -- Ensure the final selection also respects the config setting
      AND (
        (cs.use_months AND rc.resource_name LIKE '%_calendar_months') OR
        (NOT cs.use_months AND rc.resource_name NOT LIKE '%_calendar_months')
      )
    GROUP BY rc.resource_name, rc.date_bucket
    ORDER BY rc.resource_name`,
    [orgId],
  );

  return resourceCountSchema.parse(rows);
}

export async function adminFetchAllOrgs(
  {
    name,
  }: {
    name?: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<OrgRow[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();
  const params = [];
  if (name) {
    params.push(name);
  }

  const { rows: orgRows } = await db.query(
    `select
        organizations.id
        , organizations.name
        , organizations.created
        , organizations.api_url
        , (CASE WHEN resources.num_private_experiment_row_actions_calendar_months IS NULL THEN NULL ELSE (resources.num_private_experiment_row_actions_calendar_months).max_value END) span_limit
        , (CASE WHEN resources.num_log_bytes_calendar_months IS NULL THEN NULL ELSE (resources.num_log_bytes_calendar_months).max_value END) log_bytes_limit
        , (SELECT id FROM brainstore_licenses WHERE org_id = organizations.id) brainstore_license_id
        , org_billing.telemetry_url
        , org_billing.plan_id
      from organizations
      LEFT JOIN resources ON organizations.id = resources.org_id
      LEFT JOIN org_billing ON organizations.id = org_billing.org_id
      ${name ? "WHERE organizations.name = $1" : ""}
      order by name
    `,
    params,
  );

  const orgRowsParsed = z.array(orgZodSchema).parse(orgRows);
  return orgRowsParsed;
}

type OrgContextInfoParams = { name: string } | { org_id: string };
export async function adminFetchOrgContextInfo(
  params: OrgContextInfoParams,
  authLookupRaw?: AuthLookup,
): Promise<OrgContextT | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();
  const { rows: orgRows } = await db.query(
    `select
        organizations.id
        , organizations.name
        , organizations.api_url
        , organizations.is_universal_api
        , organizations.proxy_url
        , organizations.realtime_url
        , (to_jsonb(resources) - 'org_id') as resources
        , org_billing.plan_id
      from organizations
      LEFT JOIN resources ON organizations.id = resources.org_id
      LEFT JOIN org_billing ON organizations.id = org_billing.org_id
      WHERE ${"org_id" in params ? "organizations.id = $1" : "organizations.name = $1"}
      order by name
    `,
    ["org_id" in params ? params.org_id : params.name],
  );

  if (orgRows.length === 0) {
    return null;
  }
  const orgContext = orgContextSchema.parse(orgRows[0]);
  return orgContext;
}

// For now we just fetch what is required for the admin page.
export async function adminFetchProjectContextInfo(
  {
    orgName,
    projectName,
  }: {
    orgName: string;
    projectName: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<AdminProjectContext | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();
  const { rows: projectRows } = await db.query(
    `select projects.id
      from projects join organizations on projects.org_id = organizations.id
      WHERE organizations.name = $1 and projects.name = $2
    `,
    [orgName, projectName],
  );

  if (projectRows.length === 0) {
    return null;
  }
  return adminProjectContextSchema.parse(projectRows[0]);
}

const userZodSchema = z.object({
  id: z.string(),
  given_name: z.string().nullable(),
  family_name: z.string().nullable(),
  email: z.string(),
  created: z.string().datetime(),
  groups: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
      }),
    )
    .nullable(),
});
export type UserRow = z.infer<typeof userZodSchema>;

export async function adminFetchAllUsers(
  {
    orgName,
  }: {
    orgName: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<UserRow[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();

  // First, get the organization ID
  const { rows: orgRows } = await db.query(
    `SELECT id FROM organizations WHERE name = $1`,
    [orgName],
  );

  if (orgRows.length === 0) {
    return [];
  }

  const orgId = orgRows[0].id;

  // No debugging queries needed in production

  // Get the basic user information with a simpler query
  const { rows: userRows } = await db.query(
    `SELECT
      users.id,
      users.given_name,
      users.family_name,
      users.email,
      users.created,
      COALESCE(
        (
          SELECT json_agg(json_build_object('id', g.id, 'name', g.name))
          FROM (
            SELECT DISTINCT g.id, g.name
            FROM groups g
            JOIN group_users gu ON g.id = gu.group_id
            WHERE gu.user_id = users.id AND g.org_id = $1 AND g.deleted_at IS NULL
          ) g
        ),
        '[]'::json
      ) as groups
    FROM users
    JOIN members ON users.id = members.user_id
    WHERE members.org_id = $1
    ORDER BY
      CASE WHEN users.email LIKE '%@braintrustdata.com' THEN 1 ELSE 0 END,
      users.created DESC
    `,
    [orgId],
  );
  const userRowsParsed = z.array(userZodSchema).parse(userRows);
  return userRowsParsed;
}

export async function adminFetchAllProjects(
  {
    orgName,
    projectName,
  }: {
    orgName: string;
    projectName?: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<ProjectRow[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();
  const params = [orgName];
  if (projectName) {
    params.push(projectName);
  }
  const { rows: projectRows } = await db.query(
    `select projects.id, projects.org_id, projects.name, projects.created from projects
    JOIN organizations ON projects.org_id = organizations.id
    where
      projects.deleted_at is null
      and organizations.name = $1 ${projectName ? "and projects.name = $2" : ""}`,
    params,
  );
  const projectRowsParsed = z.array(projectZodSchema).parse(projectRows);
  return projectRowsParsed;
}

const OBJECT_TYPE_LIMIT = 50000;

export async function adminFetchObjects(
  {
    orgName,
    projectName,
  }: {
    orgName: string;
    projectName: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<ObjectRow[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();
  const params = [orgName, projectName];
  const { rows: objectRows } = await db.query(
    `
    WITH project_id AS (
      SELECT projects.id, projects.name, projects.created, projects.deleted_at FROM projects JOIN organizations ON projects.org_id = organizations.id
      WHERE organizations.name = $1 AND projects.name = $2 AND projects.deleted_at IS NULL
    )
      (
        SELECT 'project_logs' AS object_type, project_id.id, project_id.name, CONCAT('project_logs:', project_id.id) AS object_id, project_id.created, project_id.id AS project_id, project_id.deleted_at
        FROM project_id
      )

      UNION ALL

      (
        SELECT 'experiment' AS object_type, experiments.id, experiments.name, CONCAT('experiment:', experiments.id) AS object_id, experiments.created, experiments.project_id AS project_id, experiments.deleted_at
        FROM experiments
        WHERE experiments.project_id = (SELECT id FROM project_id)
        ORDER BY experiments.created DESC LIMIT ${OBJECT_TYPE_LIMIT}
      )

      UNION ALL

      (
        SELECT 'dataset' AS object_type, datasets.id, datasets.name, CONCAT('dataset:', datasets.id) AS object_id, datasets.created, datasets.project_id AS project_id, datasets.deleted_at
        FROM datasets
        WHERE datasets.project_id = (SELECT id FROM project_id)
        ORDER BY datasets.created DESC LIMIT ${OBJECT_TYPE_LIMIT}
      )

      UNION ALL

      (
        SELECT 'playground_logs' AS object_type, prompt_sessions.id, prompt_sessions.name, CONCAT('playground_logs:', prompt_sessions.id) AS object_id, prompt_sessions.created, prompt_sessions.project_id AS project_id, prompt_sessions.deleted_at
        FROM prompt_sessions
        WHERE prompt_sessions.project_id = (SELECT id FROM project_id)
        ORDER BY prompt_sessions.created DESC LIMIT ${OBJECT_TYPE_LIMIT}
      )
    `,
    params,
  );
  const objectRowsParsed = z.array(objectZodSchema).parse(objectRows);
  return objectRowsParsed;
}

export type OrgLookup = {
  type: "org";
  object: {
    org_name: string;
    org_id: string;
    created: string;
  };
};

export type ObjectLookup = {
  type: "object";
  object: ObjectRow & {
    org_name: string;
    org_id: string;
    project_name: string;
    project_id: string;
  };
};

export type UserLookup = {
  type: "user";
  user: UserRow & {
    orgs: {
      org_name: string;
      org_id: string;
    }[];
  };
};

export type FindObjectResult = OrgLookup | ObjectLookup | UserLookup;

export async function adminFindObject(
  {
    objectId: objectIdRaw,
  }: {
    objectId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<FindObjectResult> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  // We'll either get an object type and id, or just the id.
  let objectTypeRaw: string | null = null;
  let objectId: string;
  if (objectIdRaw.includes(":")) {
    [objectTypeRaw, objectId] = objectIdRaw.split(":");
  } else {
    objectId = objectIdRaw;
  }

  const objectType = objectTypeRaw
    ? eventObjectType.safeParse(objectTypeRaw)
    : null;

  if (objectType && !objectType.success) {
    throw new HTTPError(400, "Invalid object type");
  }

  if (
    objectId.match(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
    )
  ) {
    const db = getServiceRoleSupabase();
    const { rows: objectRows } = await db.query(
      `
      (
        SELECT 'org' AS object_type, organizations.id, organizations.name, CONCAT('org:', organizations.id) AS object_id, organizations.created, organizations.id AS project_id, NULL as deleted_at
        FROM organizations
        WHERE ($1::text IS NULL OR $1::text = 'org') AND organizations.id = $2
      )

      UNION ALL

      (
        SELECT 'project_logs' AS object_type, projects.id, projects.name, CONCAT('project_logs:', projects.id) AS object_id, projects.created, projects.id AS project_id, projects.deleted_at
        FROM projects
        WHERE ($1::text IS NULL OR $1::text = 'project_logs') AND projects.id = $2
      )

      UNION ALL

      (
        SELECT 'experiment' AS object_type, experiments.id, experiments.name, CONCAT('experiment:', experiments.id) AS object_id, experiments.created, experiments.project_id AS project_id, experiments.deleted_at
        FROM experiments
        WHERE ($1::text IS NULL OR $1::text = 'experiment') AND experiments.id = $2
      )

      UNION ALL

      (
        SELECT 'dataset' AS object_type, datasets.id, datasets.name, CONCAT('dataset:', datasets.id) AS object_id, datasets.created, datasets.project_id AS project_id, datasets.deleted_at
        FROM datasets
        WHERE ($1::text IS NULL OR $1::text = 'dataset') AND datasets.id = $2
      )

      UNION ALL

      (
        SELECT 'playground_logs' AS object_type, prompt_sessions.id, prompt_sessions.name, CONCAT('playground_logs:', prompt_sessions.id) AS object_id, prompt_sessions.created, prompt_sessions.project_id AS project_id, prompt_sessions.deleted_at
        FROM prompt_sessions
        WHERE ($1::text IS NULL OR $1::text = 'playground_logs' OR $1 = 'prompt_session') AND prompt_sessions.id = $2
      )
    `,
      [objectType?.data, objectId],
    );

    const objectRowsParsed = z.array(objectOrOrgZodSchema).parse(objectRows);

    if (objectRowsParsed.length === 0) {
      throw new HTTPError(404, "No objects found");
    } else if (objectRowsParsed.length > 1) {
      throw new HTTPError(400, "Multiple objects found");
    }

    const objectRow = objectRowsParsed[0];

    if (objectRow.object_type === "org") {
      return {
        type: "org",
        object: {
          ...objectRow,
          org_name: objectRow.name,
          org_id: objectRow.id,
          created: objectRow.created,
        },
      };
    }

    const { rows: orgProjectInfo } = await db.query(
      `
    SELECT organizations.name AS org_name, organizations.id AS org_id, projects.name AS project_name, projects.id AS project_id
    FROM organizations
    JOIN projects ON organizations.id = projects.org_id
    WHERE projects.id = $1
    `,
      [objectRow.project_id],
    );

    return {
      type: "object",
      object: {
        ...objectRow,
        org_name: orgProjectInfo[0].org_name,
        org_id: orgProjectInfo[0].org_id,
        project_name: orgProjectInfo[0].project_name,
        project_id: orgProjectInfo[0].project_id,
      },
    };
  } else {
    const db = getServiceRoleSupabase();
    const { rows: userRows } = await db.query(
      `SELECT users.id, users.given_name, users.family_name, users.email, users.created,
      (
        SELECT json_agg(json_build_object('org_name', organizations.name, 'org_id', organizations.id))
        FROM organizations
        JOIN members ON organizations.id = members.org_id
        WHERE members.user_id = users.id
      ) as orgs
      FROM users
      WHERE users.email=$1`,
      [objectId],
    );

    if (userRows.length === 0) {
      throw new HTTPError(404, "No users found");
    } else if (userRows.length > 1) {
      throw new HTTPError(400, "Multiple users found");
    }

    return {
      type: "user",
      user: {
        ...userRows[0],
        orgs: userRows[0].orgs.map(
          (org: { org_name: string; org_id: string }) => ({
            org_name: org.org_name,
            org_id: org.org_id,
          }),
        ),
      },
    };
  }
}

export async function adminRestoreObject(
  {
    objectType,
    objectId,
  }: {
    objectType: ObjectRow["object_type"];
    objectId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<{ success: true }> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();

  let tableName: string;
  switch (objectType) {
    case "experiment":
      tableName = "experiments";
      break;
    case "dataset":
      tableName = "datasets";
      break;
    case "project_logs":
      tableName = "projects";
      break;
    case "playground_logs":
      tableName = "prompt_sessions";
      break;
    default:
      throw new HTTPError(400, "Invalid object type");
  }

  await db.query(`UPDATE ${tableName} SET deleted_at = NULL WHERE id = $1`, [
    objectId,
  ]);

  return { success: true };
}

export async function adminCreateBrainstoreLicense(
  {
    orgId,
  }: {
    orgId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<{ id: string }> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const payload = {
    licenseKeyVersion: 1,
    orgId,
    created: new Date().toISOString(),
  };
  const payloadBytes = new Uint8Array(
    Buffer.from(JSON.stringify(payload, Object.keys(payload).sort())),
  );

  const privateKey = new Uint8Array(
    Buffer.from(process.env.BRAINSTORE_AUTHORIZE_PRIVATE_KEY!, "base64"),
  );

  const signature = Buffer.from(
    await signAsync(payloadBytes, privateKey),
  ).toString("base64");

  const license = Buffer.from(
    JSON.stringify({ ...payload, signature }),
  ).toString("base64");

  const params = [license, orgId];

  const db = getServiceRoleSupabase();

  const {
    rows: [licenseRow],
  } = await db.query(
    `
      insert into brainstore_licenses (license, org_id) values ($1, $2) returning id
    `,
    params,
  );

  return licenseRow;
}

const UNLIMITED = -1 as const;

export async function adminUpdateResourceLimit(
  {
    orgId,
    spanLimit,
    logBytesLimit,
  }: {
    orgId: string;
    spanLimit: number | typeof UNLIMITED;
    logBytesLimit: number | typeof UNLIMITED;
  },
  authLookupRaw?: AuthLookup,
) {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }
  const supabase = getServiceRoleSupabase();

  const isSpanLimitUnlimited = spanLimit === UNLIMITED;
  const isLogBytesLimitUnlimited = logBytesLimit === UNLIMITED;

  if (isSpanLimitUnlimited && isLogBytesLimitUnlimited) {
    await supabase.query(
      `DELETE FROM resources
       WHERE org_id = $1`,
      [orgId],
    );
    return { success: true };
  }

  if (isSpanLimitUnlimited) {
    await supabase.query(
      `INSERT INTO resources (
          org_id,
          num_private_experiment_row_actions,
          num_private_experiment_row_actions_calendar_months,
          num_production_log_row_actions,
          num_production_log_row_actions_calendar_months,
          num_dataset_row_actions,
          num_dataset_row_actions_calendar_months
        ) VALUES ($1, NULL, NULL, NULL, NULL, NULL, NULL)
        ON CONFLICT (org_id) DO UPDATE SET
          num_private_experiment_row_actions = NULL,
          num_production_log_row_actions = NULL,
          num_dataset_row_actions = NULL,
          num_private_experiment_row_actions_calendar_months = NULL,
          num_production_log_row_actions_calendar_months = NULL,
          num_dataset_row_actions_calendar_months = NULL`,
      [orgId],
    );
  } else {
    await supabase.query(
      `INSERT INTO resources (
          org_id,
          num_private_experiment_row_actions,
          num_production_log_row_actions,
          num_dataset_row_actions,
          num_private_experiment_row_actions_calendar_months,
          num_production_log_row_actions_calendar_months,
          num_dataset_row_actions_calendar_months
        ) VALUES (
             $1,
             (7, $2/4)::max_over_window,
             (7, $2/4)::max_over_window,
             (7, $2/4)::max_over_window,
             (1, $2)::max_over_calendar_months,
             (1, $2)::max_over_calendar_months,
             (1, $2)::max_over_calendar_months
        )
        ON CONFLICT (org_id) DO UPDATE SET
          num_private_experiment_row_actions = (7, $2/4)::max_over_window,
          num_production_log_row_actions = (7, $2/4)::max_over_window,
          num_dataset_row_actions = (7, $2/4)::max_over_window,
          num_private_experiment_row_actions_calendar_months = (1, $2)::max_over_calendar_months,
          num_production_log_row_actions_calendar_months = (1, $2)::max_over_calendar_months,
          num_dataset_row_actions_calendar_months = (1, $2)::max_over_calendar_months`,
      [orgId, spanLimit],
    );
  }

  if (isLogBytesLimitUnlimited) {
    await supabase.query(
      `INSERT INTO resources (
          org_id,
          num_log_bytes,
          num_log_bytes_calendar_months
        ) VALUES ($1, NULL, NULL)
        ON CONFLICT (org_id) DO UPDATE SET
          num_log_bytes = NULL,
          num_log_bytes_calendar_months = NULL`,
      [orgId],
    );
  } else {
    await supabase.query(
      `INSERT INTO resources (
          org_id,
          num_log_bytes,
          num_log_bytes_calendar_months
        ) VALUES ($1, (7, $2/4)::max_over_window, (1, $2)::max_over_calendar_months)
        ON CONFLICT (org_id) DO UPDATE SET
          num_log_bytes = (7, $2/4)::max_over_window,
          num_log_bytes_calendar_months = (1, $2)::max_over_calendar_months`,
      [orgId, logBytesLimit],
    );
  }

  return { success: true };
}

export async function adminUpdateTelemetryUrl(
  {
    orgId,
    telemetryUrl,
  }: {
    orgId: string;
    telemetryUrl?: string;
  },
  authLookupRaw?: AuthLookup,
) {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();

  // If telemetryUrl is null, undefined, or empty string, unset the column
  if (!telemetryUrl?.trim()) {
    await db.query(
      `UPDATE org_billing
       SET telemetry_url = NULL
       WHERE org_id = $1`,
      [orgId],
    );
  } else {
    // Otherwise proceed with normal UPSERT for valid telemetry URLs
    await db.query(
      `INSERT INTO org_billing (org_id, telemetry_url)
     VALUES ($1, $2)
     ON CONFLICT (org_id) DO UPDATE SET telemetry_url = $2`,
      [orgId, telemetryUrl],
    );
  }

  return { success: true };
}

export async function adminGetOrbCustomerPortal(
  {
    orgId,
  }: {
    orgId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<{ url: string }> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  if (!process.env.ORB_API_KEY) {
    throw new HTTPError(500, "Orb API key not configured");
  }

  const orb = new Orb({
    apiKey: process.env.ORB_API_KEY,
  });

  try {
    // First get the Orb customer ID for this org
    let orbCustomer;
    try {
      orbCustomer = await orb.customers.fetchByExternalId(orgId);
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        throw new HTTPError(404, "Organization not found in Orb");
      }
      throw error;
    }

    const url = orbCustomer.portal_url;
    if (!url) {
      throw new HTTPError(500, "Orb portal URL not found");
    }

    return { url };
  } catch (error) {
    console.error("Error getting Orb portal:", error);
    if (error instanceof HTTPError) {
      throw error;
    }
    throw new HTTPError(500, "Failed to get Orb portal");
  }
}

export async function adminArchiveOrg(
  {
    orgId,
  }: {
    orgId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<{ archivedName: string }> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(await isAllowedSysadmin(authLookup))) {
    throw new HTTPError(401, "Unauthorized");
  }

  const db = getServiceRoleSupabase();

  const { rows: orgRows } = await db.query(
    `SELECT name FROM organizations WHERE id = $1`,
    [orgId],
  );
  if (orgRows.length === 0) {
    throw new HTTPError(404, "Organization not found");
  }
  const orgName = orgRows[0].name;

  await db.query(
    `
    SELECT remove_member_from_org_unchecked(members.user_id, members.org_id)
    FROM members
    where members.org_id = $1
    `,
    [orgId],
  );

  const archivedName = `${orgName} (archived ${new Date().toISOString()})`;

  await db.query(`UPDATE organizations SET name = $1 WHERE id = $2`, [
    archivedName,
    orgId,
  ]);

  return { archivedName };
}
