"use client";

import { createContext, useContext, useMemo } from "react";
import {
  type OrgContextT,
  type UserContextT,
  orgContextSchema,
} from "./user-types";
import { type User } from "@braintrust/core/typespecs";

export type UserContextWithMutatorT = UserContextT & {
  invalidate: () => void;
};

export const UserContext = createContext<UserContextWithMutatorT>({
  session: null,
  user: undefined,
  orgs: {},
  status: "loading",
  invalidate: async () => undefined,
  isAdmin: false,
});

export function UserProviderServer({
  children,
  ctx,
  refreshCtx,
}: {
  children: React.ReactNode;
  ctx: UserContextT;
  refreshCtx: () => void;
}) {
  return (
    <UserContext.Provider
      value={useMemo(
        () => ({ ...ctx, invalidate: refreshCtx }),
        [ctx, refreshCtx],
      )}
    >
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  return useContext(UserContext);
}

const DefaultOrgContext = orgContextSchema.parse({ name: "" });
export const OrgContext = createContext<OrgContextT>(DefaultOrgContext);
export function OrgProvider({
  orgName,
  children,
}: {
  orgName: string;
  children: React.ReactNode;
}) {
  const { orgs } = useUser();
  const orgValue = useMemo(() => {
    const baseOrg = orgs[orgName] || DefaultOrgContext;
    return {
      ...baseOrg,
      name: baseOrg.name || orgName,
    };
  }, [orgName, orgs]);
  return <OrgContext.Provider value={orgValue}>{children}</OrgContext.Provider>;
}

export function useOrg() {
  return useContext(OrgContext);
}

export function getDisplayName(user: User) {
  if (user.email && isServiceAccount(user)) {
    return user.given_name || "Service account";
  }
  if (user.given_name && user.family_name) {
    return `${user.given_name} ${user.family_name}${
      user.email ? ` (${user.email})` : ""
    }`;
  } else if (user.email) {
    return user.email;
  }
  return user.id;
}

const SERVICE_ACCOUNT_EMAIL_REGEX = /^bt::sp::(.+)-(.+)$/;
export const isServiceAccountEmail = (email?: string | null) =>
  email && SERVICE_ACCOUNT_EMAIL_REGEX.test(email);

export function isServiceAccount(user: User) {
  return isServiceAccountEmail(user.email);
}
