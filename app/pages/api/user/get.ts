import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  getHelperStripUserType,
  getHelperStripUserTypeParamsSchema,
} from "./_util";
import { userSchema } from "@braintrust/core/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      return await getHelperStripUserType(
        {
          ...params,
          user_type: ["user"],
        },
        authLookup,
      );
    },
    {
      paramsSchema: getHelperStripUserTypeParamsSchema,
      outputSchema: userSchema.array(),
    },
  );
}
