import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
} from "../_invoke_supabase_udf";
import {
  createApiKeySchema,
  createApiKeyOutputSchema,
} from "@braintrust/core/typespecs";
import { sanitizeApiKey } from "../apikey/_util";
import { z } from "zod";

const paramsSchema = createApiKeySchema
  .merge(udfOrgIdSchema)
  .transform((data) => ({
    ...data,
    is_service_token: false,
  }));

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "create_api_key_full", {
    renameFields: { api_key_name: "name" },
    removeFields: ["update"],
    paramsSchema,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Will be validated afterwards
    postprocessOutput: (data: any) => ({
      api_key: sanitizeApiKey(data.api_key),
    }),
    outputSchema: z.object({ api_key: createApiKeyOutputSchema }),
    argnames: ["auth_id", "org_id", "name", "is_service_token"],
  });
}
