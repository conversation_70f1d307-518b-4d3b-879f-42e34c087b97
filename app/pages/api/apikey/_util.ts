import { type AuthLookup } from "../_login_to_auth_id";
import { type SqlQueryParams } from "#/utils/sql-query-params";
import {
  type QueryFilterParamValue,
  makeUserAuthIdFilter,
  makeFullResultSetQuery,
} from "../_object_crud_util";
import { makeIsOrgOwnerCTE } from "../_special_queries";
import type { UserType } from "../user/_util";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function sanitizeApiKey(r: any) {
  return Object.fromEntries(
    Object.entries(r).filter((e) => e[0] !== "key_hash"),
  );
}

// RBAC_DISCLAIMER: A user can fetch only their own API keys.
export function makeApiKeyFullResultSetQuery({
  authLookup,
  startingParams,
  org_id,
  org_name,
  id,
  name,
}: {
  authLookup: AuthLookup;
  startingParams?: SqlQueryParams;
  org_id?: QueryFilterParamValue<string> | null;
  org_name?: QueryFilterParamValue<string> | null;
  id?: QueryFilterParamValue<string> | null;
  name?: QueryFilterParamValue<string> | null;
}): {
  query: string;
  queryParams: SqlQueryParams;
  notFoundErrorMessage: string;
} {
  return makeFullResultSetQuery({
    authLookup,
    permissionInfo: "org-membership",
    startingParams,
    priorObjectTables: ["api_key", "user", "organization"],
    fullResultSetAdditionalProjections: [
      "users.email as user_email",
      "users.given_name as user_given_name",
      "users.family_name as user_family_name",
    ],
    filters: {
      org_id,
      org_name,
      id,
      name,
      auth_id: makeUserAuthIdFilter(authLookup.auth_id),
    },
  });
}

// RBAC_DISCLAIMER: Org owners can fetch all API keys for an organization.
export function makeOwnerApiKeysFullResultSetQuery({
  authLookup,
  org_id,
  id,
  name,
  user_type,
}: {
  authLookup: AuthLookup;
  org_id: string;
  user_type: UserType;
  id?: string | null;
  name?: string | null;
}): {
  query: string;
  queryParams: SqlQueryParams;
  notFoundErrorMessage: string;
} {
  const { query: isOrgOwnerCTE, queryParams: isOrgOwnerQueryParams } =
    makeIsOrgOwnerCTE({
      userId: authLookup.user_id,
      orgId: org_id,
    });

  const { query: resultSetQuery, queryParams: resultSetQueryParams } =
    makeFullResultSetQuery({
      authLookup,
      permissionInfo: "org-membership",
      startingParams: isOrgOwnerQueryParams,
      priorObjectTables: ["api_key", "user", "organization"],
      fullResultSetAdditionalProjections: [
        "users.email as user_email",
        "users.given_name as user_given_name",
        "users.family_name as user_family_name",
        "users.user_type as user_type",
      ],
      filters: {
        org_id,
        id,
        name,
      },
    });

  const query = `
    ${isOrgOwnerCTE}
    select api_keys_with_user_details.* from (
      ${resultSetQuery}
      and "api_keys"."org_id" is not null
    ) as api_keys_with_user_details
    join is_org_owner on is_org_owner.exists
    where user_type = '${user_type}'::user_type_enum
  `;

  return {
    query,
    queryParams: resultSetQueryParams,
    notFoundErrorMessage: "No API keys found or missing organization ownership",
  };
}
