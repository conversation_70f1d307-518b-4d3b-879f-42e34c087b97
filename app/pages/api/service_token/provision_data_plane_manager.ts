import type { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { extractSingularRow } from "../_object_crud_util";
import { runJsonRequest } from "../_request_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { isAllowedSysadmin } from "#/utils/derive-error-context";
import { HTTPError } from "#/utils/http_error";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { isBraintrustDataPlane } from "#/utils/user-types";
import { addMembers } from "../organization/member_actions";
import { adminFetchOrgContextInfo } from "#/app/admin/actions";
import { getOrganization, isOrganizationOwner } from "#/app/app/actions";

const DATA_PLANE_MANAGER_NAME = "data_plane_manager";
const DATA_PLANE_MANAGER_SERVICE_TOKEN = "bt_data_plane_service_token";

const paramsSchema = z.object({
  org_id: z.string(),
  force_recreate_token: z.boolean().nullish(),
});

const dataPlaneManagerSchema = z.object({
  id: z.string(),
  name: z.string(),
});
const dataPlaneManagerWithAuthIdSchema = dataPlaneManagerSchema.extend({
  auth_id: z.string(),
});

const dataPlaneServiceTokenSchema = z.object({
  id: z.string(),
  name: z.string(),
  key: z.string().nullish(),
  created: z.boolean(),
});

export type DataPlaneManager = z.infer<typeof dataPlaneManagerSchema>;
export type DataPlaneServiceToken = z.infer<typeof dataPlaneServiceTokenSchema>;

const outputSchema = z.object({
  data_plane_manager: dataPlaneManagerSchema,
  data_plane_service_token: dataPlaneServiceTokenSchema,
});

/*
 * RBAC_DISCLAIMER: This endpoint is only accessible to sysadmins.
 * On the Braintrust data plane it will:
 * - create a service account with sysadmin privileges
 * - create an unscoped service token for the service account
 *
 * TODO: Self-hosted data plane support is not fully implemented but would
 * allow org owners to provision a data plane manager and service token.
 * On self-hosted data planes it will:
 * - create a service account in the Viewer group of the specified org
 * - create an unscoped service token for the service account
 */
export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const isSysadmin = await isAllowedSysadmin(authLookup);
      const isOrgOwner = await isOrganizationOwner(
        { org_id: params.org_id },
        authLookup,
      );
      if (!isSysadmin && !isOrgOwner) {
        throw new HTTPError(401, "Unauthorized");
      }

      let apiUrl: string | null | undefined = undefined;
      if (isSysadmin) {
        const orgContext = await adminFetchOrgContextInfo(
          { org_id: params.org_id },
          authLookup,
        );
        if (!orgContext) {
          throw new HTTPError(400, "Failed to fetch organization");
        }
        apiUrl = orgContext.api_url;
      } else if (isOrgOwner) {
        const org = await getOrganization(
          { org_id: params.org_id },
          authLookup,
        );
        if (!org) {
          throw new HTTPError(400, "Failed to fetch organization");
        }
        apiUrl = org.api_url;
      }

      // TODO: provision data plane manager for self-hosted data planes
      const selfHosted = apiUrl && !isBraintrustDataPlane(apiUrl);
      if (selfHosted) {
        throw new HTTPError(400, "Bad Request");
      }

      if (!isSysadmin) {
        throw new HTTPError(401, "Unauthorized");
      }

      const supabase = getServiceRoleSupabase();
      let dataPlaneManager: DataPlaneManager | null = null;
      let dataPlaneManagerAuthId: string | null = null;
      let dataPlaneServiceToken: DataPlaneServiceToken | null = null;
      try {
        if (!selfHosted) {
          const accountQuery = `
            select id, auth_id, given_name as name from users
            where user_type = 'service_account' and id = bt_service_account_user_id() and auth_id = bt_service_account_auth_id() and email = bt_service_account_email()
          `;

          const row = await extractSingularRow({
            rows: (await supabase.query(accountQuery)).rows,
            notFoundErrorMessage: "Failed to find data plane manager",
          });
          const parsedRow = dataPlaneManagerWithAuthIdSchema.safeParse(row);
          if (!parsedRow.success) {
            console.error(
              "Failed to parse data plane manager response",
              parsedRow.error,
            );
            throw new HTTPError(
              400,
              "Failed to parse data plane manager response",
            );
          }
          dataPlaneManager = {
            id: parsedRow.data.id,
            name: parsedRow.data.name,
          };
          dataPlaneManagerAuthId = parsedRow.data.auth_id;
        } else {
          const queryParams = new SqlQueryParams();
          const orgIdParam = queryParams.add(params.org_id);
          const { rows } = await supabase.query(
            `with org_members as (
              select user_id from members
              where org_id = ${orgIdParam}
            )
            select users.id, users.auth_id, users.given_name as name from users
            join org_members on users.id = org_members.user_id
            where users.user_type = 'service_account' and users.email like 'bt::sp::${DATA_PLANE_MANAGER_NAME}-%'
          `,
            queryParams.params,
          );
          if (rows.length === 1) {
            const parsedRow = dataPlaneManagerWithAuthIdSchema.safeParse(
              rows[0],
            );
            if (!parsedRow.success) {
              console.error(
                "Failed to parse data plane manager response",
                parsedRow.error,
              );
              throw new HTTPError(
                400,
                "Failed to parse data plane manager response",
              );
            }
            dataPlaneManager = {
              id: parsedRow.data.id,
              name: parsedRow.data.name,
            };
            dataPlaneManagerAuthId = parsedRow.data.auth_id;
          } else if (rows.length > 1) {
            throw new HTTPError(
              400,
              "Multiple data plane managers found for org",
            );
          } else {
            const output = await addMembers(
              {
                orgId: params.org_id,
                users: {
                  service_accounts: [
                    {
                      name: DATA_PLANE_MANAGER_NAME,
                      token_name: null, // disable service token creation here so we can provision an unscoped token below
                    },
                  ],
                  group_names: ["Viewers"],
                },
              },
              authLookup,
              {
                client: supabase,
              },
            );
            if (output.added_users.length !== 1) {
              throw new HTTPError(
                400,
                `Failed to provision data plane manager for org ${params.org_id}`,
              );
            }
            const row = await extractSingularRow({
              rows: (
                await supabase.query(
                  `select id, auth_id, given_name as name from users where id = $1`,
                  [output.added_users[0].id],
                )
              ).rows,
              notFoundErrorMessage:
                "Data plane manager deleted after creation, please retry provisioning.",
            });

            const parsedRow = dataPlaneManagerWithAuthIdSchema.safeParse(row);
            if (!parsedRow.success) {
              console.error(
                "Failed to parse data plane manager response",
                parsedRow.error,
              );
              throw new HTTPError(
                400,
                "Failed to parse data plane manager response",
              );
            }
            dataPlaneManager = {
              id: parsedRow.data.id,
              name: parsedRow.data.name,
            };
            dataPlaneManagerAuthId = parsedRow.data.auth_id;
          }
        }

        const forceRecreateToken = params.force_recreate_token || false;
        const queryParams = new SqlQueryParams();
        const tokenNameParam = queryParams.add(
          DATA_PLANE_MANAGER_SERVICE_TOKEN,
        );
        const dataPlaneManagerIdParam = queryParams.add(dataPlaneManager.id);
        const dataPlaneManagerAuthIdParam = queryParams.add(
          dataPlaneManagerAuthId,
        );
        const query = `
        with
        deleted_data_plane_service_token as (
          delete from api_keys
          where user_id = ${dataPlaneManagerIdParam} and name = ${tokenNameParam} and org_id is null
          and ${forceRecreateToken ? "true" : "false"}
          returning id
        ),
        data_plane_service_token as (
          select id, name, null as key, false as created from api_keys
          where user_id = ${dataPlaneManagerIdParam} and name = ${tokenNameParam} and org_id is null
        ),
        created_data_plane_service_token as (
          select (api_key->>'id')::uuid as id, api_key->>'name' as name, api_key->>'key' as key, true as created from (
            select create_api_key_unchecked(${dataPlaneManagerAuthIdParam}, null, ${tokenNameParam}, true)->'api_key' as api_key
            where not exists (select 1 from data_plane_service_token) or ${forceRecreateToken ? "true" : "false"}
          ) st
        )
        select
          coalesce(created_data_plane_service_token.id, data_plane_service_token.id) as id,
          coalesce(created_data_plane_service_token.name, data_plane_service_token.name) as name,
          coalesce(created_data_plane_service_token.key, data_plane_service_token.key) as key,
          coalesce(created_data_plane_service_token.created, data_plane_service_token.created) as created
        from
          created_data_plane_service_token full outer join data_plane_service_token on true
        `;

        const row = await extractSingularRow({
          rows: (await supabase.query(query, queryParams.params)).rows,
          notFoundErrorMessage: "Failed to provision data plane service token",
        });

        dataPlaneServiceToken = {
          id: row.id,
          name: row.name,
          key: row.key,
          created: row.created,
        };

        return {
          data_plane_manager: dataPlaneManager,
          data_plane_service_token: dataPlaneServiceToken,
        };
      } catch (e) {
        console.error(
          "Failed to provision data plane manager",
          e,
          dataPlaneManager,
          dataPlaneServiceToken,
        );
        throw e;
      }
    },
    {
      paramsSchema,
      outputSchema,
    },
  );
}
